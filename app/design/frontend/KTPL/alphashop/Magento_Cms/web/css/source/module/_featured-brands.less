& when (@media-common =true) {
    .featured-brands {
     

        .section-heading {
            .lib-vendor-prefix-display(flex);
            justify-content: space-between;
            align-items: center;

            .heading-wrapper {
                .lib-vendor-prefix-display(flex);
                flex-direction: column;
            }

            .featured-brands-action {
                .lib-css(color, @color-black);
                .lib-font-size(14);
                text-decoration: none;
                position: relative;
                padding-right: 20px;
                display: inline-block;
                .lib-css(font-weight, @font-weight__semibold);

                &:after {
                    content: '\2192';
                    position: absolute;
                    right: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    transition: transform 0.3s ease;
                }

                &:hover {
                    text-decoration: none;
                    color: @color-black;

                    &:after {
                        transform: translate(5px, -50%);
                    }
                }
            }
        }

        // Featured Brands Slider
        .featured-brands-slider {
            margin: 30px 0 40px;
        

            .brand-slide-item {
                padding: 0 10px;
            }

            .featured-brands-slider-container:not(.slick-initialized) {
                overflow: hidden;
                height: 118px;
                .lib-vendor-prefix-display();
            }

            &::before {
                content: "";
                background-color: @gray-100;
                height: 42%;
                width: 100%;
                position: absolute;
                left: 0;
                right: 0;
                top: 60%;
                z-index: -1;
            }
        }

        // Featured Brands Grid
        .featured-brands-grid {
            .lib-vendor-prefix-display(grid);
            grid-template-columns: repeat(1, 1fr);
            gap: @indent__base;
            width: 100%;
        }

        .featured-brand-item {
            position: relative;
            overflow: hidden;

            &:hover {
                .featured-brand-image {
                    transform: scale(1.5);
                }
            }
        }

        .featured-brand-image {
            width: 100%;
            height: auto;
            display: block;
            transition: transform 0.3s ease;
        }

        .featured-brand-info {
            position: absolute;
            
            background-color: rgba(255, 255, 255, 0.9);
            text-align: left;
            .lib-vendor-prefix-display(flex);
            .lib-vendor-prefix-flex-direction();
            align-items: center;
            
            justify-content: center;
            bottom: 15px;
            left: 15px;
            max-width: 180px;
            padding: 20px 15px;
            height: 100%;
            max-height: 150px;
            
        }

        .featured-brand-logo {
            max-width: 100px;
            height: auto;
            margin-bottom: 8px;
        }

        .featured-brand-description {
            .lib-font-size(16);
            margin-bottom: @indent__base;
        }

        .featured-brand-link {
            .lib-css(color, @color-black);
            .lib-font-size(14);
            text-decoration: none;
            position: relative;
            padding-right: 20px;
            display: inline-block;

            &:after {
                content: '\2192';
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                transition: transform 0.3s ease;
            }

            &:hover {
                text-decoration: none;
                color: @color-black;

                &:after {
                    transform: translate(5px, -50%);
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum ='max') and (@break =@screen__m) {
    .featured-brands {
        .section-heading {
            align-items: start;
            text-align: left;
            .lib-vendor-prefix-flex-direction();

            .featured-brands-action {
                .lib-font-size(12);
                margin-top: 15px;
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum ='min') and (@break =@screen__m) {
    .featured-brands {
        .featured-brands-grid {
            grid-template-columns: repeat(3, 1fr);
        }

        .section-heading {
            .featured-brands-action {
                .lib-font-size(14);
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum ='min') and (@break =@screen__l) {
    .featured-brands {
        .featured-brands-grid {
            gap: 60px;
        }

        .featured-brand-info{
            max-width: 240px;
            width: 100%;
            max-height: 180px;
            height: 100%;
            bottom: 25px;
            left: 25px;
            padding: 0;
        }

        .featured-brand-logo {
            max-width: 120px;
        }

        .section-heading {
            .featured-brands-action {
                .lib-font-size(16);
            }
        }
    }
}