/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

define([
    'jquery',
    'mage/smart-keyboard-handler',
    'mage/mage',
    'jquery-ui-modules/widget',
    'domReady!',
    "slick"
], function ($, keyboardHandler) {
    console.log("theme js")
    'use strict';

    $('.cart-summary').mage('sticky', {
        container: '#maincontent'
    });

    $('.panel.header > .header.links').clone().appendTo('#store\\.links');
    $('#store\\.links li a').each(function () {
        var id = $(this).attr('id');

        if (id !== undefined) {
            $(this).attr('id', id + '_mobile');
        }
    });
    keyboardHandler.apply();

    // Slick slider initialization for category grid
    function initCategorySlider() {
        var $categoriesGrid = $('.slick-slider-mobile');

        if (!$categoriesGrid.length) {
            return;
        }

        if (window.innerWidth <= 1023) {
            if (!$categoriesGrid.hasClass('slick-initialized')) {
                $categoriesGrid.slick({
                    dots: false,
                    arrows: false,
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    mobileFirst: true,
                    responsive: [
                        {
                            breakpoint: 768,
                            settings: {
                                slidesToShow: 3,
                                slidesToScroll: 1
                            }
                        },
                        {
                            breakpoint: 480,
                            settings: {
                                slidesToShow: 2,
                                slidesToScroll: 2
                            }
                        }
                    ]
                });
            }
        } else {
            if ($categoriesGrid.hasClass('slick-initialized')) {
                $categoriesGrid.slick('unslick');
            }
        }
    }

    // Featured Brands Slider initialization
    function initFeaturedBrandsSlider() {
        var $brandsSlider = $('.featured-brands-slider-container');

        if (!$brandsSlider.length) {
            return;
        }

        // Always ensure proper initialization regardless of screen size
        if ($brandsSlider.hasClass('slick-initialized')) {
            $brandsSlider.slick('unslick');
        }

        $brandsSlider.slick({
            dots: false,
            arrows: false,
            infinite: true,
            speed: 300,
            slidesToShow: 6,
            slidesToScroll: 1,
            autoplay: true,
            slidesMargin: 10,
            autoplaySpeed: 3000,
            centerMode: false,
            cssEase: 'linear',
            responsive: [
                {
                    breakpoint: 1280,
                    settings: {
                        slidesToShow: 4,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 768,
                    settings: {
                        slidesToShow: 3,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 480,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                    }
                }
            ]
        });
    }

    // Function to initialize all sliders
    function initAllSliders() {
        initCategorySlider();
        initFeaturedBrandsSlider();
    }

    // Initialize all sliders on page load
    initAllSliders();

    // Debounced window resize handler
    var resizeTimer;
    $(window).on('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            initAllSliders();
        }, 250);
    });





    $(document).ready(function () {
        var currentYear = new Date().getFullYear();
        $('.dynamic-copyright').text(currentYear);
    });

    // Utility Announcements functionality
    $('.utility-announcements-wrapper').each(function() {
        var $element = $(this);

        $element.find('.close').on('click', function() {
            $element.slideUp();
        });
    });



});
