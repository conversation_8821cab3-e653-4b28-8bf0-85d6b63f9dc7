// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .actions-toolbar {
        > .primary,
        > .secondary {
            text-align: center;

            .action {
                &:extend(.abs-button-responsive all);
                margin-bottom: @indent__s;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            margin-bottom: @indent__s;
            &:last-child {
                margin-bottom: 0;
            }
        }

        > .secondary {
            .action.back {
                display: none;
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .actions-toolbar {
        .lib-actions-toolbar();
        > .primary,
        > .secondary {
            margin-bottom: 0;

            .action {
                margin-bottom: 0;
                width: auto;
            }
        }
    }

    form {
        .actions-toolbar {
            .column:not(.sidebar-main) &,
            .column:not(.sidebar-additional) & {
                &:extend(.abs-margin-for-forms-desktop all);
            }
        }
    }
}
