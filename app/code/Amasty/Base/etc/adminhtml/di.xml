<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Magento 2 Base Package
 */-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\AdminNotification\Block\Grid\Renderer\Actions">
        <plugin name="Amasty_Base::show-unsubscribe-link" type="Amasty\Base\Plugin\AdminNotification\Block\Grid\Renderer\Actions"/>
    </type>

    <type name="Magento\AdminNotification\Block\Grid\Renderer\Notice">
        <plugin name="Amasty_Base::add-amasty-class" type="Amasty\Base\Plugin\AdminNotification\Block\Grid\Renderer\Notice"/>
    </type>

    <type name="Magento\AdminNotification\Block\ToolbarEntry">
        <plugin name="Amasty_Base::add-amasty-class-logo" type="Amasty\Base\Plugin\AdminNotification\Block\ToolbarEntry"/>
    </type>

    <type name="Magento\ImportExport\Controller\Adminhtml\Import\Download">
        <arguments>
            <argument name="componentRegistrar" xsi:type="object">Amasty\Base\Component\ComponentRegistrar</argument>
        </arguments>
    </type>

    <type name="Magento\Config\Block\System\Config\Form\Field">
        <plugin name="Amasty_Base::replace-image-path" type="Amasty\Base\Plugin\Config\Block\System\Config\Form\Field"/>
    </type>

    <type name="Magento\Backend\Block\Widget\Form\Element\Dependence">
        <plugin name="Amasty_Base::fix-dependence" type="Amasty\Base\Plugin\Adminhtml\Block\Widget\Form\Element\Dependence"/>
    </type>

    <type name="Magento\Backend\Model\Menu\Builder">
        <plugin name="Amasty_Base::menu_builder" type="Amasty\Base\Plugin\Backend\Model\Menu\Builder"/>
    </type>

    <type name="Magento\Config\Model\Config\Structure">
        <plugin name="Amasty_Base:advertise" type="Amasty\Base\Plugin\Backend\Model\Config\StructurePlugin"/>
        <plugin name="Amasty_Base:infoblock" type="Amasty\Base\Plugin\Backend\Model\Config\AddInformationBlockPlugin"/>
    </type>
    <type name="Magento\Config\Block\System\Config\Tabs">
        <plugin name="Amasty_Base::build_amasty_tabs"
                type="Amasty\Base\Plugin\Config\Block\System\Config\BuildAmastyTabsPlugin"/>
    </type>

    <type name="Amasty\Base\Model\AmastyMenu\ActiveSolutionsProvider">
        <arguments>
            <argument name="solutionsVersions" xsi:type="array">
                <item name="10" xsi:type="string">Lite</item>
                <item name="20" xsi:type="string">Pro</item>
                <item name="30" xsi:type="string">Premium</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="Amasty\Base\Model\System\Message\Mage248FixNotification" type="Amasty\Base\Model\System\Message\CloseableNotification">
        <arguments>
            <argument name="displayValidator" xsi:type="object">Amasty\Base\Model\System\Message\DisplayValidator\Mage248FixValidator</argument>
            <argument name="identity" xsi:type="string">AMASTY_MAGE248FIX_NTOFICIATION</argument>
            <argument name="message" xsi:type="string"><![CDATA[<p>Amasty Notice: After upgrading to <strong>Magento 2.4.8</strong>, you may encounter frontend and Admin Panel issues related to caching, styles, and table prefix processing, which can impact site functionality and layout.</p><p>To resolve these and improve compatibility with <strong>Amasty extensions</strong>, install the ‘amasty/module-mage-248-fix’ module via composer using the command: <strong>composer require amasty/module-mage-248-fix -W</strong>.</p> <p>For more details on all the issues addressed by the module, refer to the <strong>README</strong> file within the extension.</p><p>{{closeLink}}</p>]]></argument>
            <argument name="closeLinkText" xsi:type="string"><![CDATA[Click on the link <a href="">to ignore this notification</a>]]></argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\Notification\MessageList">
        <arguments>
            <argument name="messages" xsi:type="array">
                <item name="mage248fix" xsi:type="string">Amasty\Base\Model\System\Message\Mage248FixNotification</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Config\Block\System\Config\Edit">
        <plugin name="Amasty_Base::removeSaveButton" type="Amasty\Base\Plugin\Config\Block\System\Config\Edit\RemoveSaveButton"/>
    </type>
    <type name="ClassyLlama\AvaTax\Plugin\Model\ConfigPlugin">
        <plugin name="Amasty_Base::disableAmastySectionCheck" type="Amasty\Base\Plugin\ClassyLlama\Plugin\Model\ConfigPlugin\DisableAmastySectionCheck"/>
    </type>
    <type name="Avalara\AvaTax\Plugin\Model\ConfigPlugin">
        <plugin name="Amasty_Base::disableAmastySectionCheck" type="Amasty\Base\Plugin\Avalara\Plugin\Model\ConfigPlugin\DisableAmastySectionCheck"/>
    </type>
</config>
