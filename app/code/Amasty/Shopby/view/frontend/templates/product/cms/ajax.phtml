<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Improved Layered Navigation Base for Magento 2
 */
/**
 * @var \Amasty\Shopby\Block\Product\ProductList\Ajax $block
 * @var \Magento\Framework\Escaper $escaper
 */
if (!$block->canShowBlock()) {
    return;
}
?>
<script type="text/x-magento-init">
{
    "body.cms-index-index": {
        "amShopbyAjax": {
                "submitByClick": <?= /* @noEscape */ (int) $block->submitByClick() ?>,
                "clearUrl": "<?= $escaper->escapeUrl($block->getClearUrl()) ?>",
                "isCategorySingleSelect": "<?= /* @noEscape */ $block->isCategorySingleSelect() ?>"
            }
        }
}
</script>
