<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Mega Menu Core Base for Magento 2
 */-->

<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form_data_source</item>
            <item name="deps" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">Custom Menu Item</item>
        <item name="config" xsi:type="array">
            <item name="dataScope" xsi:type="string">data</item>
            <item name="namespace" xsi:type="string">amasty_megamenu_link_form</item>
        </item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
        <item name="buttons" xsi:type="array">
            <item name="back" xsi:type="array">
                <item name="name" xsi:type="string">back</item>
                <item name="label" xsi:type="string" translate="true">Back</item>
                <item name="class" xsi:type="string">back</item>
                <item name="url" xsi:type="string">*/*/</item>
            </item>
            <item name="save" xsi:type="string">Amasty\MegaMenuLite\Block\Adminhtml\Link\SaveButton</item>
            <item name="save_and_continue" xsi:type="string">Amasty\MegaMenuLite\Block\Adminhtml\Link\SaveAndContinueButton</item>
            <item name="delete" xsi:type="string">Amasty\MegaMenuLite\Block\Adminhtml\Link\DeleteButton</item>
        </item>
    </argument>
    <dataSource name="amasty_megamenu_link_form_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Amasty\MegaMenuLite\Ui\DataProvider\Form\Link\DataProvider</argument>
            <argument name="name" xsi:type="string">amasty_megamenu_link_form_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">entity_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="submit_url" xsi:type="url" path="ammegamenu/link/save"/>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Amasty_MegaMenuLite/js/form/link/provider</item>
                <item name="fieldsToRemoveIfNotVisible" xsi:type="array">
                    <item name="internal_url" xsi:type="string">internal_url</item>
                    <item name="external_url" xsi:type="string">external_url</item>
                </item>
            </item>
        </argument>
    </dataSource>
    <fieldset name="general">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="label" xsi:type="string" translate="true">General</item>
                <item name="collapsible" xsi:type="boolean">true</item>
                <item name="opened" xsi:type="boolean">true</item>
                <item name="sortOrder" xsi:type="string">10</item>
            </item>
        </argument>

        <field name="entity_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="visible" xsi:type="boolean">false</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">mega_menu_link</item>
                    <item name="dataScope" xsi:type="string">entity_id</item>
                    <item name="sortOrder" xsi:type="string">10</item>
                </item>
            </argument>
        </field>

        <field name="store_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="visible" xsi:type="boolean">false</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">mega_menu_link</item>
                    <item name="default" xsi:type="string">0</item>
                    <item name="dataScope" xsi:type="string">store_id</item>
                    <item name="sortOrder" xsi:type="string">20</item>
                </item>
            </argument>
        </field>

        <field name="name">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Title</item>
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">mega_menu_link</item>
                    <item name="dataScope" xsi:type="string">name</item>
                    <item name="sortOrder" xsi:type="string">30</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>

        <field name="link_type">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">URL Key</item>
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="formElement" xsi:type="string">select</item>
                    <item name="elementTmpl" xsi:type="string">Amasty_MegaMenuLite/form/element/select</item>
                    <item name="default" xsi:type="number">0</item>
                    <item name="sortOrder" xsi:type="string">35</item>
                    <item name="switcherConfig" xsi:type="array">
                        <item name="enabled" xsi:type="boolean">true</item>
                        <item name="rules" xsi:type="array">
                            <item name="0" xsi:type="array">
                                <item name="value" xsi:type="string">0</item>
                                <item name="actions" xsi:type="array">
                                    <item name="0" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.internal_url</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                    <item name="1" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.page_id</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                    <item name="2" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.landing_page</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                    <item name="3" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.external_url</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                </item>
                            </item>
                            <item name="1" xsi:type="array">
                                <item name="value" xsi:type="string">1</item>
                                <item name="actions" xsi:type="array">
                                    <item name="0" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.internal_url</item>
                                        <item name="callback" xsi:type="string">show</item>
                                    </item>
                                    <item name="1" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.page_id</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                    <item name="2" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.landing_page</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                    <item name="3" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.external_url</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                </item>
                            </item>
                            <item name="2" xsi:type="array">
                                <item name="value" xsi:type="string">2</item>
                                <item name="actions" xsi:type="array">
                                    <item name="0" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.page_id</item>
                                        <item name="callback" xsi:type="string">show</item>
                                    </item>
                                    <item name="1" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.internal_url</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                    <item name="2" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.landing_page</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                    <item name="3" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.external_url</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                </item>
                            </item>
                            <item name="3" xsi:type="array">
                                <item name="value" xsi:type="string">3</item>
                                <item name="actions" xsi:type="array">
                                    <item name="0" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.page_id</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                    <item name="1" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.internal_url</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                    <item name="2" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.landing_page</item>
                                        <item name="callback" xsi:type="string">show</item>
                                    </item>
                                    <item name="3" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.external_url</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                </item>
                            </item>
                            <item name="4" xsi:type="array">
                                <item name="value" xsi:type="string">4</item>
                                <item name="actions" xsi:type="array">
                                    <item name="0" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.page_id</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                    <item name="1" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.internal_url</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                    <item name="2" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.landing_page</item>
                                        <item name="callback" xsi:type="string">hide</item>
                                    </item>
                                    <item name="3" xsi:type="array">
                                        <item name="target" xsi:type="string">amasty_megamenu_link_form.amasty_megamenu_link_form.general.external_url</item>
                                        <item name="callback" xsi:type="string">show</item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </item>
                </item>
                <item name="options" xsi:type="object">Amasty\MegaMenuLite\Model\OptionSource\UrlKey</item>
            </argument>
        </field>

        <field name="internal_url">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Internal URL</item>
                    <item name="notice" xsi:type="string" translate="true">Relative to Web Site Base URL.</item>
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">mega_menu_link</item>
                    <item name="dataScope" xsi:type="string">link</item>
                    <item name="sortOrder" xsi:type="string">40</item>
                </item>
            </argument>
        </field>

        <field name="external_url">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">External URL</item>
                    <item name="notice" xsi:type="string" translate="true">Please insert full URL address.</item>
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">mega_menu_link</item>
                    <item name="dataScope" xsi:type="string">link</item>
                    <item name="sortOrder" xsi:type="string">40</item>
                    <item name="validation" xsi:type="array">
                        <item name="validate-url" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>

        <field name="status">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Status</item>
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="formElement" xsi:type="string">select</item>
                    <item name="elementTmpl" xsi:type="string">Amasty_MegaMenuLite/form/element/select</item>
                    <item name="default" xsi:type="number">1</item>
                    <item name="sortOrder" xsi:type="string">50</item>
                </item>
                <item name="options" xsi:type="object">Amasty\MegaMenuLite\Model\OptionSource\Status</item>
            </argument>
        </field>

        <field name="label">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Menu Label Text</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">mega_menu</item>
                    <item name="dataScope" xsi:type="string">label</item>
                    <item name="sortOrder" xsi:type="string">60</item>
                </item>
            </argument>
        </field>

        <container name="label_group" component="Magento_Ui/js/form/components/group">
            <argument name="data" xsi:type="array">
                <item name="type" xsi:type="string">group</item>
                <item name="config" xsi:type="array">
                    <item name="additionalClasses" xsi:type="string">admin__control-grouped-date ammenu-label-group</item>
                    <item name="required" xsi:type="boolean">false</item>
                    <item name="breakLine" xsi:type="boolean">false</item>
                    <item name="sortOrder" xsi:type="string">70</item>
                </item>
            </argument>

            <field name="label_background_color">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="label" xsi:type="string" translate="true">Label Background Color (hex)</item>
                        <item name="additionalClasses" xsi:type="string">admin__field-group-show-label</item>
                        <item name="component" xsi:type="string">Amasty_MegaMenuLite/js/form/element/color-picker</item>
                        <item name="elementTmpl" xsi:type="string">Amasty_MegaMenuLite/form/element/color-picker</item>
                        <item name="colorPickerConfig" xsi:type="array">
                            <item name="preferredFormat" xsi:type="string">hex</item>
                            <item name="colorPickerMode" xsi:type="string">full</item>
                        </item>
                        <item name="template" xsi:type="string">ui/form/field</item>
                        <item name="dataType" xsi:type="string">text</item>
                        <item name="formElement" xsi:type="string">input</item>
                        <item name="source" xsi:type="string">mega_menu</item>
                        <item name="dataScope" xsi:type="string">label_background_color</item>
                        <item name="sortOrder" xsi:type="string">80</item>
                    </item>
                </argument>
            </field>

            <field name="label_text_color">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="label" xsi:type="string" translate="true">Label Text Color (hex)</item>
                        <item name="additionalClasses" xsi:type="string">admin__field-group-show-label</item>
                        <item name="component" xsi:type="string">Amasty_MegaMenuLite/js/form/element/color-picker</item>
                        <item name="elementTmpl" xsi:type="string">Amasty_MegaMenuLite/form/element/color-picker</item>
                        <item name="colorPickerConfig" xsi:type="array">
                            <item name="preferredFormat" xsi:type="string">hex</item>
                            <item name="colorPickerMode" xsi:type="string">full</item>
                        </item>
                        <item name="template" xsi:type="string">ui/form/field</item>
                        <item name="dataType" xsi:type="string">text</item>
                        <item name="formElement" xsi:type="string">input</item>
                        <item name="source" xsi:type="string">mega_menu</item>
                        <item name="dataScope" xsi:type="string">label_text_color</item>
                        <item name="sortOrder" xsi:type="string">80</item>
                    </item>
                </argument>
            </field>
        </container>
    </fieldset>
</form>
