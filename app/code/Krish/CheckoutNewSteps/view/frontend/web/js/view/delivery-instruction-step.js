/*
 * @category   Krish Technolabs Module Development
 * @package    Krish_ModuleName
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

define(
    [
        'ko',
        'uiComponent',
        'underscore',
        'Magento_Checkout/js/model/step-navigator'
    ],
    function (
        ko,
        Component,
        _,
        stepNavigator
    ) {
        'use strict';
        return Component.extend({
            defaults: {
                template: 'Krish_CheckoutNewSteps/delivery-instruction-step',
                stepTitle: 'Delivery Instruction'
            },

            isVisible: ko.observable(true),

            initialize: function () {
                this._super();
                stepNavigator.registerStep(
                    'new_step',
                    null,
                    'New Step',
                    this.isVisible,
                    _.bind(this.navigate, this),
                    15
                );
                return this;
            },

            navigate: function () {
                this.isVisible(true);
            },

            navigateToNextStep: function () {
                stepNavigator.next();
            }
        });
    }
);
