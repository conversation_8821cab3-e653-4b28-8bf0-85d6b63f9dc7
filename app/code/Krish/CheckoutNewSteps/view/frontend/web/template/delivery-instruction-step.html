<!--
  ~ @category   Krish Technolabs Module Development
  ~ @package    Krish_ModuleName
  ~ <AUTHOR> Technolabs Pvt Ltd.
  ~ @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
  ~ @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
  -->
<li id="new-step" if="isVisible">
    <div class="step-title" data-bind="i18n: 'New Step'" data-role="title"></div>
    <div id="checkout-step-new-step"
         class="step-content"
         data-role="content">

        <form data-bind="submit: navigateToNextStep" novalidate="novalidate">
            <div id="new-step-form">
                <!-- ko foreach: getRegion('delivery-instruction-step-form') -->
                    <!-- ko template: getTemplate() --><!-- /ko -->
                <!-- /ko -->
            </div>
            <div class="actions-toolbar">
                <div class="primary">
                    <button data-role="opc-continue" type="submit" class="button action continue primary">
                        <span><!-- ko i18n: 'Next'--><!-- /ko --></span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</li>
