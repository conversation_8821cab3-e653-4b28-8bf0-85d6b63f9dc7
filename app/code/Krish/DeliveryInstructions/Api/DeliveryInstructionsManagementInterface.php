<?php
/**
 * @category   Krish
 * @package    Krish_DeliveryInstructions
 * <AUTHOR> Technolabs Pvt Ltd.
 * @copyright  Copyright (c) 2023 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 * @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace Krish\DeliveryInstructions\Api;

use Krish\DeliveryInstructions\Api\Data\DeliveryInstructionsInterface;

/**
 * Service contract for managing delivery instructions
 */
interface DeliveryInstructionsManagementInterface
{
    /**
     * Save delivery instructions to quote
     *
     * @param int $cartId
     * @param \Krish\DeliveryInstructions\Api\Data\DeliveryInstructionsInterface $deliveryInstructions
     * @return bool
     */
    public function saveDeliveryInstructions(
        int $cartId,
        DeliveryInstructionsInterface $deliveryInstructions
    ): bool;

    /**
     * Get delivery instructions from quote
     *
     * @param int $cartId
     * @return \Krish\DeliveryInstructions\Api\Data\DeliveryInstructionsInterface
     */
    public function getDeliveryInstructions(int $cartId): DeliveryInstructionsInterface;
}
