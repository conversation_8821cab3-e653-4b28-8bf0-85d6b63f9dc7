<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ProductQuestions\Test\Unit\Model;

use PHPUnit\Framework\TestCase;
use Krish\ProductQuestions\Model\Question;
use Krish\ProductQuestions\Api\Data\QuestionInterface;

/**
 * Question model unit test
 */
class QuestionTest extends TestCase
{
    /**
     * @var Question
     */
    private $question;

    /**
     * Set up test
     */
    protected function setUp(): void
    {
        $this->question = new Question();
    }

    /**
     * Test question setters and getters
     */
    public function testQuestionSettersAndGetters()
    {
        $customerEmail = '<EMAIL>';
        $customerName = 'Test Customer';
        $productSku = 'TEST-SKU';
        $questionText = 'Test question?';
        $answer = 'Test answer';
        $status = QuestionInterface::STATUS_APPROVED;

        $this->question->setCustomerEmail($customerEmail);
        $this->question->setCustomerName($customerName);
        $this->question->setProductSku($productSku);
        $this->question->setQuestion($questionText);
        $this->question->setAnswer($answer);
        $this->question->setStatus($status);

        $this->assertEquals($customerEmail, $this->question->getCustomerEmail());
        $this->assertEquals($customerName, $this->question->getCustomerName());
        $this->assertEquals($productSku, $this->question->getProductSku());
        $this->assertEquals($questionText, $this->question->getQuestion());
        $this->assertEquals($answer, $this->question->getAnswer());
        $this->assertEquals($status, $this->question->getStatus());
    }

    /**
     * Test status methods
     */
    public function testStatusMethods()
    {
        $this->question->setStatus(QuestionInterface::STATUS_PENDING);
        $this->assertTrue($this->question->isPending());
        $this->assertFalse($this->question->isApproved());
        $this->assertFalse($this->question->isRejected());

        $this->question->setStatus(QuestionInterface::STATUS_APPROVED);
        $this->assertFalse($this->question->isPending());
        $this->assertTrue($this->question->isApproved());
        $this->assertFalse($this->question->isRejected());

        $this->question->setStatus(QuestionInterface::STATUS_REJECTED);
        $this->assertFalse($this->question->isPending());
        $this->assertFalse($this->question->isApproved());
        $this->assertTrue($this->question->isRejected());
    }

    /**
     * Test status label
     */
    public function testStatusLabel()
    {
        $this->question->setStatus(QuestionInterface::STATUS_PENDING);
        $this->assertEquals('Pending', $this->question->getStatusLabel());

        $this->question->setStatus(QuestionInterface::STATUS_APPROVED);
        $this->assertEquals('Approved', $this->question->getStatusLabel());

        $this->question->setStatus(QuestionInterface::STATUS_REJECTED);
        $this->assertEquals('Rejected', $this->question->getStatusLabel());
    }

    /**
     * Test visible on frontend
     */
    public function testVisibleOnFrontend()
    {
        $this->question->setVisibleOnFrontend(true);
        $this->assertTrue($this->question->getVisibleOnFrontend());

        $this->question->setVisibleOnFrontend(false);
        $this->assertFalse($this->question->getVisibleOnFrontend());
    }
}
