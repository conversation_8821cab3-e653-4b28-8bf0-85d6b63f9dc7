<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ProductQuestions\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;

/**
 * Product Questions data helper
 */
class Data extends AbstractHelper
{
    /**
     * Configuration paths
     */
    const XML_PATH_ENABLED = 'krish_product_questions/general/enabled';
    const XML_PATH_REQUIRE_LOGIN = 'krish_product_questions/general/require_login';
    const XML_PATH_AUTO_APPROVE = 'krish_product_questions/general/auto_approve';
    const XML_PATH_QUESTIONS_PER_PAGE = 'krish_product_questions/general/questions_per_page';
    const XML_PATH_ALLOW_GUEST_QUESTIONS = 'krish_product_questions/general/allow_guest_questions';
    const XML_PATH_EMAIL_ENABLED = 'krish_product_questions/email/enabled';
    const XML_PATH_SENDER_IDENTITY = 'krish_product_questions/email/sender_identity';
    const XML_PATH_ADMIN_EMAIL = 'krish_product_questions/email/admin_email';
    const XML_PATH_COPY_TO = 'krish_product_questions/email/copy_to';
    const XML_PATH_COPY_METHOD = 'krish_product_questions/email/copy_method';
    const XML_PATH_SHOW_CUSTOMER_NAME = 'krish_product_questions/frontend/show_customer_name';
    const XML_PATH_SHOW_QUESTION_DATE = 'krish_product_questions/frontend/show_question_date';
    const XML_PATH_ENABLE_ACCORDION = 'krish_product_questions/frontend/enable_accordion';
    const XML_PATH_TAB_TITLE = 'krish_product_questions/frontend/tab_title';

    /**
     * Check if module is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isEnabled($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if login is required
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isLoginRequired($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_REQUIRE_LOGIN,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if auto approve is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isAutoApproveEnabled($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_AUTO_APPROVE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get questions per page
     *
     * @param int|null $storeId
     * @return int
     */
    public function getQuestionsPerPage($storeId = null)
    {
        return (int) $this->scopeConfig->getValue(
            self::XML_PATH_QUESTIONS_PER_PAGE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if guest questions are allowed
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isGuestQuestionsAllowed($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ALLOW_GUEST_QUESTIONS,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if email notifications are enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isEmailEnabled($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_EMAIL_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get sender identity
     *
     * @param int|null $storeId
     * @return string
     */
    public function getSenderIdentity($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_SENDER_IDENTITY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get admin email
     *
     * @param int|null $storeId
     * @return string
     */
    public function getAdminEmail($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_ADMIN_EMAIL,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get copy to emails
     *
     * @param int|null $storeId
     * @return string
     */
    public function getCopyTo($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_COPY_TO,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get copy method
     *
     * @param int|null $storeId
     * @return string
     */
    public function getCopyMethod($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_COPY_METHOD,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if customer name should be shown
     *
     * @param int|null $storeId
     * @return bool
     */
    public function showCustomerName($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_SHOW_CUSTOMER_NAME,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if question date should be shown
     *
     * @param int|null $storeId
     * @return bool
     */
    public function showQuestionDate($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_SHOW_QUESTION_DATE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if accordion is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isAccordionEnabled($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLE_ACCORDION,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get tab title
     *
     * @param int|null $storeId
     * @return string
     */
    public function getTabTitle($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_TAB_TITLE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        ) ?: __('Product Questions');
    }
}
