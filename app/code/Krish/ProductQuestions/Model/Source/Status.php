<?php
/*
 *
 *  @category   Krish Technolabs Module Development
 *  @package    Krish_ProductQuestions
 *  <AUTHOR> Technolabs Pvt Ltd.
 *  @copyright  Copyright (c) 2025 Krish Technolabs Pvt Ltd. (https://www.krishtechnolabs.com/)
 *  @license    https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace Krish\ProductQuestions\Model\Source;

use Magento\Framework\Data\OptionSourceInterface;
use Krish\ProductQuestions\Api\Data\QuestionInterface;

/**
 * Status source model
 */
class Status implements OptionSourceInterface
{
    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray()
    {
        return [
            ['value' => QuestionInterface::STATUS_PENDING, 'label' => __('Pending')],
            ['value' => QuestionInterface::STATUS_APPROVED, 'label' => __('Approved')],
            ['value' => QuestionInterface::STATUS_REJECTED, 'label' => __('Rejected')]
        ];
    }

    /**
     * Get options as array
     *
     * @return array
     */
    public function toArray()
    {
        return [
            QuestionInterface::STATUS_PENDING => __('Pending'),
            QuestionInterface::STATUS_APPROVED => __('Approved'),
            QuestionInterface::STATUS_REJECTED => __('Rejected')
        ];
    }
}
