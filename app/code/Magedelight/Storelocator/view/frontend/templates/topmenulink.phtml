<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

$counter = $block->getCounter();
?>
<?php if ($block->isModuleEnable() && $block->isTopLinkDisplay()): ?>
    <li>
        <a <?= /* @noEscape */
        $block->getLinkAttributes() ?>>
            <?= /* @noEscape */
            $escaper->escapeHtml($block->getMenuLinkLabel()) ?>
            <?= /* @noEscape */
            ($counter) ? '<span class="counter qty">' . $escaper->escapeHtml($counter) . '</span>' : ''; ?>
        </a>
    </li>
<?php endif; ?>
