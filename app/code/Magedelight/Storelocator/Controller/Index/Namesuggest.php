<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Controller\Index;

use Magedelight\Storelocator\Model\ResourceModel\Storelocator\CollectionFactory;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\ResultFactory;
use Magedelight\Storelocator\Model\Source\Region;
use Magedelight\Storelocator\Helper\Storelocator;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\Result\Layout;
use Magento\Store\Model\StoreManagerInterface;
use Magedelight\Storelocator\Model\ResourceModel\TagAttributeValue\CollectionFactory as ValueCollection;
use \Laminas\Uri\Uri;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\Stdlib\DateTime\DateTime;

class Namesuggest extends Action
{
    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * @var Region
     */
    protected $regionOptions;

    /**
     * @var Storelocator
     */
    protected $storehelper;

    /**
     * @var $storeManager
     */
    protected $storeManager;

    /**
     * @var ValueCollection
     */
    protected $valueCollection;

    /**
     * @var Uri
     */
    protected $zendUri;

    /**
     * @var Json
     */
    protected $json;

    /**
     * @var array
     */
    private $hours = [];

    /**
     * @var DateTime
     */
    protected $date;

    /**
     * @var string[]
     */
    private $days = [
        'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
    ];

    /**
     * Constructor
     *
     * @param Context $context
     * @param CollectionFactory $collectionFactory
     * @param Region $regionOptions
     * @param Storelocator $storehelper
     * @param StoreManagerInterface $storeManager
     * @param ValueCollection $valueCollection
     * @param Uri $zendUri
     */
    public function __construct(
        Context $context,
        CollectionFactory $collectionFactory,
        Region $regionOptions,
        Storelocator $storehelper,
        StoreManagerInterface $storeManager,
        ValueCollection $valueCollection,
        Uri $zendUri,
        Json $json,
        DateTime $date
    ) {
        $this->collectionFactory = $collectionFactory;
        $this->regionOptions = $regionOptions;
        $this->storehelper = $storehelper;
        $this->storeManager = $storeManager;
        $this->valueCollection = $valueCollection;
        $this->zendUri = $zendUri;
        $this->json = $json;
        $this->date = $date;
        parent::__construct($context);
    }

    /**
     * Name Suggest Method.
     *
     * @return ResponseInterface|ResultInterface|Layout
     * @throws NoSuchEntityException|LocalizedException
     */
    public function execute()
    {
        $flag = true;
        $params = $this->getRequest()->getParams();

        $storeId = $this->storeManager->getStore()->getId();
        $collection = $this->collectionFactory->create()
                    ->addFieldToFilter('is_active', 1)
                    ->addStoreFilter($storeId);
        if (isset($params['search_text'])) {
            $searchText = $params['search_text'];

            if ($searchText) {
                $collection->addFieldToFilter(
                    ['storename', 'address', 'city', 'region','zipcode','country_id','country'],
                    [
                        ['like' => '%'.$searchText.'%'],
                        ['like' => '%'.$searchText.'%'],
                        ['like' => '%'.$searchText.'%'],
                        ['like' => '%'.$searchText.'%'],
                        ['like' => '%'.$searchText.'%'],
                        ['like' => '%'.$searchText.'%'],
                        ['like' => '%'.$searchText.'%'],
                    ]
                );
            }
        }
        if (isset($params['searchBySubmit'])) {
            $this->zendUri->setQuery($params['searchBySubmit']);
            $formatedParams = $this->zendUri->getQueryAsArray();
            $searchText = $formatedParams['pac-input'];
            if (isset($searchText)) {
                $collection->addFieldToFilter(
                    ['storename', 'address', 'city', 'region','zipcode','country_id','country'],
                    [
                        ['like' => '%'.$searchText.'%'],
                        ['like' => '%'.$searchText.'%'],
                        ['like' => '%'.$searchText.'%'],
                        ['like' => '%'.$searchText.'%'],
                        ['like' => '%'.$searchText.'%'],
                        ['like' => '%'.$searchText.'%'],
                        ['like' => '%'.$searchText.'%'],
                    ]
                );
            }
        }

        $pageSize = $this->storehelper->getStorePerPageValue();
        $currentPage = $params['current_page'] ?? 0;
        $collection->setCurPage($currentPage);
        $collection->setPageSize($pageSize);

        $result = [];
        $totalRecords = $collection->getSize();
        $result['currentPage'] = $currentPage;
        $result['data'] = $this->prepareData($collection->getData());
        $result['totalRecords'] = $totalRecords;
        $result['totalPages'] = ($pageSize > 0 || $totalRecords > 0) ? ceil($totalRecords / $pageSize) : 1;

        $resultJson = $this->resultFactory->create(ResultFactory::TYPE_JSON);
        $resultJson->setData($result);
        return $resultJson;
    }

    /**
     * Prepare Data.
     *
     * @param mixed $data
     * @return mixed
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    private function prepareData($data)
    {
        foreach ($data as $key => $value) {
            if (isset($value['address'])) {
                if (!empty($value['address'])) {
                    $address = explode('\n', $value['address']);
                    $data[$key]['address'] = $address;
                }
            }
            if (isset($value['country_id'])) {
                if (!empty($value['country_id'])) {
                    $data[$key]['country_name'] = $this->storehelper->getCountryByCode($value['country_id']);
                }
            }
            if (isset($value['region_id'])) {
                if (!empty($value['region_id'])) {
                    $options = $this->regionOptions->getOptions();
                    $data[$key]['region_name'] = $options[$value['region_id']];
                }
            }
            if (isset($value['region'])) {
                if (!empty($value['region'])) {
                    $data[$key]['region_name'] = $value['region'];
                }
            }
            $data[$key]['store_image'] = $this->storehelper->getStoreImage($value['storeimage']);
            if (isset($value['url_key'])) {
                if (!empty($value['url_key'])) {
                    $data[$key]['store_url'] = $this->getBaseUrl() . $value['url_key'];
                }
            }

            if (!empty($value['storetime'])) {
                $data[$key]['storetime'] = $this->getStoreHours($value['storetime']) ?: "";
                $data[$key]['timelabel'] = $this->getTitleHours() ?: "";
            }
        }


        return $data;
    }

    /**
     * Get Base URL.
     *
     * @return mixed
     * @throws NoSuchEntityException
     */
    public function getBaseUrl()
    {
        return $this->storeManager->getStore()->getBaseUrl();
    }

     /**
     * Get Store Hours.
     */
    public function getStoreHours($time)
    {
        $daysHtml = '';

        if ($time) {
            $hours = $this->json->unserialize($time);
            if ($hours) {
                foreach ($hours as $day) {
                    $daysHtml .= '<p>' . $this->renderHtml($day) . '</p>';
                }
            }
        }

        return $daysHtml;
    }

    /**
     * Render HTML.
     *
     * @param array $dayArray
     * @return string
     */
    public function renderHtml($dayArray = [])
    {
        $day = null;
        $hour = null;

        if ($dayArray && $dayArray['days']) {
            $day = $this->prepareDayLabel($dayArray['days']);

            if ($dayArray['day_status']) {
                $openTime = $this->prepareOpentimeLabel($dayArray['open_hour'], $dayArray['open_minute']);
                $closeTime = $this->prepareOpentimeLabel($dayArray['close_hour'], $dayArray['close_minute']);
                $hour = sprintf('<span class="open-time">%s</span>', __($openTime . ' - ' . $closeTime));
            } else {
                $hour = $this->closeDayLabel();
            }
        }

        return sprintf("%s %s", __($day), __($hour));
    }

    /**
     * Prepare Day label.
     *
     * @param string $day
     * @return string
     */
    public function prepareDayLabel($day)
    {
        return sprintf('<span class="day" style="width:78px;display:inline-block;">%s</span>', __($day));
    }

    /**
     * Prepare Open Time Label.
     *
     * @param string $openHour
     * @param string $openMinute
     * @return string
     */
    public function prepareOpentimeLabel($openHour, $openMinute)
    {
        return $openHour.":".$openMinute;
    }

    /**
     * Get Title Hours.
     *
     * @return array|string
     */
    public function getTitleHours()
    {
        $hours = $this->hours;
        $weekOfDay = $this->date->date('w');
        $currentDay = $this->days[$weekOfDay];

        if (!empty($hours)) {
            $filteredHours = array_filter($hours, function ($var) use ($currentDay) {
                return ($var['days'] == $currentDay && $var['day_status'] == 1);
            });

            if (empty($filteredHours)) {
                return sprintf("%s", $this->closeDayLabel());
            } else {
                $firstHour = reset($filteredHours); // Get the first element of the filtered array
                $openTime = $this->prepareOpentimeLabel($firstHour['open_hour'], $firstHour['open_minute']);
                $closeTime = $this->prepareOpentimeLabel($firstHour['close_hour'], $firstHour['close_minute']);
                return sprintf("%s - %s", $openTime, $closeTime);
            }
        }

        return sprintf("%s", $this->closeDayLabel());

    }

    /**
     * Close Day Label.
     */
    public function closeDayLabel()
    {
        return sprintf('<span class="close-label">%s</span>', __("Closed"));
    }

}
