<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\ViewModel;

use Magedelight\Storelocator\Api\StorelocatorRepositoryInterface;
use Magedelight\Storelocator\Helper\Storelocator as LocatorHelper;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Registry;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Catalog\Model\Product;
use Magento\Directory\Model\CountryFactory;
use Magento\Framework\Module\Manager as ModuleManager;
use Magento\Framework\App\ObjectManager;

class StoreList implements ArgumentInterface
{
    /**
     * @var StorelocatorRepositoryInterface
     */
    private $storelocatorRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;

    /**
     * @var Registry
     */
    protected $registry;

    /**
     * @var Product
     */
    private $product;

    /**
     * @var array
     */
    protected $storeIds = [];

    /**
     * @var LocatorHelper
     */
    protected $helper;

    /**
     * @var CountryFactory
     */
    protected $countryFactory;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var ModuleManager
     */
    private $moduleManager;

    /**
     * StoreList constructor.
     * @param StorelocatorRepositoryInterface $storelocatorRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param LocatorHelper $helper
     * @param Registry $registry
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param CountryFactory $countryFactory
     */
    public function __construct(
        StorelocatorRepositoryInterface $storelocatorRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        LocatorHelper $helper,
        Registry $registry,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        CountryFactory $countryFactory,
        ModuleManager $moduleManager
    ) {
        $this->storelocatorRepository = $storelocatorRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->helper = $helper;
        $this->registry = $registry;
        $this->storeManager = $storeManager;
        $this->countryFactory = $countryFactory;
        $this->moduleManager = $moduleManager;
    }

    /**
     * Getitems
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getItems()
    {
        $searchCriteria = $this->searchCriteriaBuilder
                        ->addFilter('is_active', 1, 'eq')
                        ->create();
        $searchResult = $this->storelocatorRepository->getList($searchCriteria);
        return $searchResult->getItems();
    }

    /**
     * Get current product
     *
     * @return mixed
     * @throws LocalizedException
     */
    public function getCurrentProduct()
    {
        if ($this->product===null) {
            $this->product = $this->registry->registry('product');

            if (!$this->product->getId()) {
                throw new LocalizedException(__('Failed to initialize product'));
            }
        }

        return $this->product->getId();
    }

    /**
     * Get All validate
     *
     * @return array
     * @throws LocalizedException
     */
    public function getAllValidateStoreIds()
    { 
        if (empty($this->storeIds)) {
            $stores = $this->getItems();
            $currentProduct = $this->getCurrentProduct();
            $msiSourceCode = [];

            if($this->product && $this->isMsiEnabled()){
                $sourceItems = $this->getSourcesItems($this->product->getSku());
                foreach ($sourceItems as $sourceItem) {
                        $msiSourceCode[] = $sourceItem->getData('source_code');
                }
            }

            foreach ($stores as $store) {

                if($store->getData('msi_source')){
                    if(!in_array($store->getData('msi_source'), $msiSourceCode)){
                        continue;
                    }
                }

                /* @var $store \Magedelight\Storelocator\Model\Storelocator */
                $allProducts = $store->getAllValidateProductIds();

                /*if ($store->getIsActive() &&
                    in_array($currentProduct, $allProducts) &&
                    in_array($this->getStoreId(), $store->getData('store_ids'))) {
                    $this->storeIds[] =  $store;
                }*/

                if(!$this->checkRuleActions($store)){
                    continue;
                }

                if ($store->getIsActive() &&
                    in_array($currentProduct, $allProducts) && (in_array($this->getStoreId(), $store->getData('store_ids')) || in_array(0, $store->getData('store_ids')))) {
                    $this->storeIds[] =  $store;
                }
            }
        }
        return $this->storeIds;
    }

    /**
     * @param  $rule
     *
     * @return boolean
     */
    public function checkRuleActions($store)
    {
        $validate = true;
        if (!$store->getConditions()->validate($this->product)) {
            return false;
        }
        return $validate;
    }

    /**
     * Show product
     *
     * @return bool
     */
    public function showProductAvailability()
    {
        return $this->helper->isStoreAvailabilityEnabled();
    }

    /**
     * Filter output
     *
     * @param string $string
     * @return mixed
     */
    public function filterOutputHtml($string)
    {
        $pos = strpos($string, '\n');

        if ($pos !== false && substr($string, $pos + 1) !== 'n') {
            return str_replace('\n', ', ', $string);
        }
        
        return str_replace('\n', '', $string);
    }

    /**
     * Get Country
     *
     * @param string $countryCode
     * @return string
     */
    public function getCountryByCode($countryCode)
    {
        return $this->countryFactory->create()->loadByCode($countryCode)->getName();
    }

    /**
     * Get store
     *
     * @return int
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getStoreId()
    {
        return $this->storeManager->getStore()->getId();
    }

    /**
     * Get Source items
     *
     * @param string $sku
     * @return mixed
     */
    public function getSourcesItems($sku)
    {
        $searchCriteria = $this->searchCriteriaBuilder->addFilter('sku', $sku)->create();
        $sourceItemRepository = ObjectManager::getInstance()->get('\Magento\InventoryApi\Api\SourceItemRepositoryInterface');
        $sourceItemData = $sourceItemRepository->getList($searchCriteria);
        return $sourceItemData->getItems();
    }

    public function getMSIStockQty() {
        $sku = $this->product->getSku();
        $stockData = [];
        $searchCriteria = $this->searchCriteriaBuilder->addFilter('sku', $sku)->create();
        $sourceItemRepository = ObjectManager::getInstance()->get('\Magento\InventoryApi\Api\SourceItemRepositoryInterface');
        $sourceItemData = $sourceItemRepository->getList($searchCriteria);
        foreach ($sourceItemData->getItems() as $sourceItem){
            $stockData[$sourceItem->getSourceCode()] = $sourceItem->getQuantity();
        }
        return $stockData;
    }

    public function isMsiEnabled()
    {
        return $this->moduleManager->isEnabled('Magento_Inventory');
    }
}
