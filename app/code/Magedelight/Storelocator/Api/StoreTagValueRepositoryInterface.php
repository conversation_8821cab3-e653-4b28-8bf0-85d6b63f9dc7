<?php
/**
 * @package Magedelight_Storelocator for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storelocator\Api;

use Magedelight\Storelocator\Api\Data\StoreTagValueInterface;
use Magedelight\Storelocator\Api\Data\StoreTagValueSearchResultInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

interface StoreTagValueRepositoryInterface
{
    /**
     * Save tag attribute vale.
     *
     * @param StoreTagValueInterface $tagValue
     * @return StoreTagValueInterface
     * @throws LocalizedException
     */
    public function save(Data\StoreTagValueInterface $tagValue);

    /**
     * Retrieve tag value.
     *
     * @param int $tagValueId
     * @return StoreTagValueInterface
     * @throws LocalizedException
     */
    public function getById($tagValueId);

    /**
     * Retrieve tags value matching the specified criteria.
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return StoreTagValueSearchResultInterface
     * @throws LocalizedException
     */
    public function getList(SearchCriteriaInterface $searchCriteria);

    /**
     * Delete tag value.
     *
     * @param StoreTagValueInterface $tagValue
     * @return bool true on success
     * @throws LocalizedException
     */
    public function delete(Data\StoreTagValueInterface $tagValue);

    /**
     * Delete tag value by ID.
     *
     * @param int $tagValueId
     * @return bool true on success
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function deleteById($tagValueId);
}
