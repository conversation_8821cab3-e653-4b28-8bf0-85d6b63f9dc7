<?xml version="1.0"?>

<!--
/**
* Magedelight
* Copyright (C) 2019 Magedelight <<EMAIL>>
*
* @category Magedelight
* @package Magedelight_StorepickupGraphQl
* @copyright Copyright (c) 2019 Mage Delight (http://www.magedelight.com/)
* @license http://opensource.org/licenses/gpl-3.0.html GNU General Public License,version 3 (GPL-3.0)
* <AUTHOR> <<EMAIL>>
*/
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Module/etc/module.xsd">
    <module name="Magedelight_StorepickupGraphQl" setup_version="1.0.1">
        <sequence>
            <module name="Magento_Theme"/>
            <module name="Magento_Config"/>
            <module name="Magento_Core"/>
            <module name="Magento_Backend"/>
            <module name="Magento_GraphQl"/>
            <module name="Magedelight_Storepickup"/>
        </sequence>
    </module>
</config>
