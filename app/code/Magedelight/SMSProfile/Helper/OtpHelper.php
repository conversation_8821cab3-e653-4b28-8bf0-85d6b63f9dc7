<?php
/**
 * OtpHelper
 * Copyright (C) 2022 Magedelight <<EMAIL>>
 *
 * @category  Magedelight
 * @package   Magedelight_SMSProfile
 * @copyright Copyright (c) 2022 Mage Delight (http://www.magedelight.com/)
 * @license   http://opensource.org/licenses/gpl-3.0.html GNU General Public License,version 3 (GPL-3.0)
 * <AUTHOR> <<EMAIL>>
 */

namespace Magedelight\SMSProfile\Helper;

use Magedelight\SMSProfile\Helper\Sender\Email;
use Magedelight\SMSProfile\Helper\Sender\Message;
use Magedelight\SMSProfile\Model\Otp;
use Magedelight\SMSProfile\Model\ResourceModel\Otp as OtpResource;
use Magedelight\SMSProfile\Model\OtpFactory;
use Magedelight\SMSProfile\Model\TransactionType;
use Magedelight\SMSProfile\ViewModel\FrontendHelper;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory as CustomerCollectionFactory;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;

class OtpHelper
{
    public const CONTACT_TYPE_EMAIL = 'email';
    public const CONTACT_TYPE_PHONE = 'phone';
    private const DATE_FORMAT = "Y-m-d H:i:s";
    private const BLOCKED_OTP_TILL = 1440; // 24 hours

    /**
     * @var ConfigHelper
     */
    private ConfigHelper $configHelper;
    /**
     * @var TimezoneInterface
     */
    private TimezoneInterface $timezone;
    /**
     * @var OtpFactory
     */
    private OtpFactory $otpFactory;
    /**
     * @var OtpResource
     */
    private OtpResource $otpResource;
    /**
     * @var CustomerCollectionFactory
     */
    private CustomerCollectionFactory $customerCollectionFactory;
    /**
     * @var Otp
     */
    private Otp $otpInstance;
    /**
     * @var CustomerInterface
     */
    private CustomerInterface $customer;
    /**
     * @var FrontendHelper
     */
    private FrontendHelper $frontendHelper;
    /**
     * @var Email
     */
    private Email $emailSender;
    /**
     * @var Message
     */
    private Message $smsSender;

    /**
     * OtpHelper constructor.
     * @param ConfigHelper $configHelper
     * @param TimezoneInterface $timezone
     * @param OtpResource $otpResource
     * @param OtpFactory $otpFactory
     * @param CustomerCollectionFactory $customerCollectionFactory
     * @param FrontendHelper $frontendHelper
     * @param Email $emailSender
     * @param Message $smsSender
     */
    public function __construct(
        ConfigHelper $configHelper,
        TimezoneInterface $timezone,
        OtpResource $otpResource,
        OtpFactory $otpFactory,
        CustomerCollectionFactory $customerCollectionFactory,
        FrontendHelper $frontendHelper,
        Email $emailSender,
        Message $smsSender
    ) {
        $this->configHelper = $configHelper;
        $this->timezone = $timezone;
        $this->otpFactory = $otpFactory;
        $this->otpResource = $otpResource;
        $this->customerCollectionFactory = $customerCollectionFactory;
        $this->frontendHelper = $frontendHelper;
        $this->emailSender = $emailSender;
        $this->smsSender = $smsSender;
    }

    public function getContactType($contact)
    {
        if (filter_var($contact, FILTER_VALIDATE_EMAIL)) {
            return self::CONTACT_TYPE_EMAIL;
        } elseif (preg_match('/^[+\d]?(?:[\d.\s()\-\[\]]*)$/', $contact)) {
            return self::CONTACT_TYPE_PHONE;
        }

        return false;
    }

    /**
     * Get Current OTP instance by mobile number
     *
     * @param string $mobile
     * @return Otp|DataObject
     */
    private function retrieveOTP($mobile)
    {
        if (!isset($this->otpInstance)) {
            /** @var Otp $otpInstance */
            $this->otpInstance = $this->otpFactory->create();
            $this->otpResource->load($this->otpInstance, $mobile, 'customer_mobile');
        }
        return $this->otpInstance;
    }

    /**
     * Send otp function
     *
     * @param string $contact
     * @param string $action
     * @return string
     * @throws \Exception
     */
    public function sendOtp(string $contact, string $action)
    {
        $resendAttempts = $this->checkExceedAttempts($contact);

        $otpCode = $this->generateOTP();

        $otpInstance = $this->saveOTP($contact, $otpCode, $resendAttempts + 1);
        $smsSent = false;
        $emailSent = false;
        if ($this->frontendHelper->isEmailEnable() &&
            !in_array($action, [TransactionType::SIGN_UP, TransactionType::COD_OTP])
        ) {
            try {
                $emailSent = $this->sendViaEmail($contact, $otpInstance, $action);
            } catch (\Exception $e) {
                //TODO: log exception
                $emailSent = false;
            }
        }
        if ($this->frontendHelper->isSmsEnable()) {
            try {
                $smsSent = $this->sendViaMessage($contact, $otpInstance, $action);
            } catch (\Exception $e) {
                //TODO: log exception
                $smsSent = false;
            }
        }

        if (!$smsSent && !$emailSent) {
            // TODO: exception not able to send otp.
        }

        return $otpInstance;
    }

    /**
     * Verify OTP by mobile number
     *
     * @param string $mobile
     * @param string $otp
     * @return Otp|DataObject
     * @throws LocalizedException
     * @throws \Magento\Framework\Exception\AlreadyExistsException
     */
    public function verifyOtp($mobile, $otp)
    {
        $otpInstance = $this->retrieveOTP($mobile);

        if (!$otpInstance->getOtp()) {
            throw new LocalizedException(__("OTP was not sent to the number."));
        }

        $expiry = $this->configHelper->getOTPExpiry() * 60; // Convert minutes to seconds
        $now = $this->timezone->date()->getTimestamp();
        $otpSent = $this->timezone->date($otpInstance->getCreatedAt())->getTimestamp();
        if (($now - $otpSent) > $expiry) {
            throw new LocalizedException(__("OTP is expired."));
        }

        if ($otpInstance->getOtp() != $otp) {
            throw new LocalizedException(__("OTP is not valid."));
        }

        if ($otpInstance->getOtp() == $otp) {
            $this->saveOTP($mobile, $otp, 0);
            return $otpInstance;
        }
    }

    private function generateOTP()
    {
        $length = $this->configHelper->getOtpLength();
        $format = $this->configHelper->getOtpFormat();
        if ($format == 'alphanum') {
            $characters = array_merge(range('a', 'z'), range('0', '9'));
        } elseif ($format == 'alpha') {
            $characters = array_merge(range('a', 'z'));
        } else {
            $characters = array_merge(range('0', '9'));
        }

        $otp_string = '';
        $max = count($characters) - 1;
        for ($i = 0; $i < $length; $i++) {
            $otp_string .= $characters[random_int(0, $max)];
        }

        return $otp_string;
    }

    public function setCustomer(CustomerInterface $customer)
    {
        $this->customer = $customer;
    }

    /**
     * Get customer id with mobile number.
     *
     * @param string $phone
     * @param mixed $code
     * @return int
     * @throws NoSuchEntityException
     */
    public function getCustomerIdByPhone($phone, $code = null)
    {
        $customerCollection = $this->customerCollectionFactory->create();

        $customerCollection->addAttributeToSelect('*')
            ->addAttributeToFilter('customer_mobile', ['eq' => $phone]);
        if ($code) {
            $customerCollection->addAttributeToFilter('countryreg', ['eq' => $code]);
        }

        if ($customerCollection->getSize()) {
            return $customerCollection->getFirstItem()->getId();
        }

        throw new NoSuchEntityException(__('No customer with the specified phone number.'));
    }

    /**
     * Save or update current otp instance.
     *
     * @param string $contact
     * @param string $otpCode
     * @param int $resendAttempts
     * @return Otp
     * @throws \Magento\Framework\Exception\AlreadyExistsException
     */
    private function saveOTP(string $contact, string $otpCode, int $resendAttempts)
    {
        $customerId = false;
        if (isset($this->customer) && $this->customer) {
            $customerId = $this->customer->getId();
        }
        $otpInstance = $this->retrieveOTP($contact);
        $otpInstance->setCustomerMobile($contact)
            ->setOtp($otpCode)
            ->setAttempts($resendAttempts)
            ->setCreatedAt(
                $this->timezone->date('', '', false)
                    ->format(self::DATE_FORMAT)
            );
        if ($customerId) {
            $otpInstance->setCustomerId($customerId);
        }
        $this->otpResource->save($otpInstance);
        return $otpInstance;
    }

    private function checkExceedAttempts($contact)
    {
        if ($this->configHelper->isSandbox()) {
            return 0;
        }
        $otp = $this->retrieveOTP($contact);
        if (!$otp->getAttempts()) {
            return 0;
        }
        $canRetryAfter = self::BLOCKED_OTP_TILL * 60; // Convert minutes to seconds
        $now = $this->timezone->date()->getTimestamp();
        $lastOtpSent = $this->timezone->date($otp->getCreatedAt())->getTimestamp();
        if (($now - $lastOtpSent) > $canRetryAfter) {
            //Tried again after a safe timespan. Restart the attempts.
            return 0;
        }
        if ($otp->getAttempts() >= $this->configHelper->getResendLimit()) {
            //Exceed attempts true.
            $leftMinutes = ($canRetryAfter - ($now - $lastOtpSent)) / 60;
            $leftHours = ceil($leftMinutes / 60);
            throw new \Exception(
                __('OTP resend limit exhausted. Kindly try after %1 hours', (int) $leftHours)
            );
        }
        return $otp->getAttempts();
    }

    /**
     * Send otp via email
     *
     * @param string $contact
     * @param Otp $otpInstance
     * @param string $action
     * @throws \Exception
     */
    private function sendViaEmail(string $contact, Otp $otpInstance, string $action)
    {
        if ($this->getContactType($contact) != self::CONTACT_TYPE_EMAIL) {
            $contact = false;
        }
        if (isset($this->customer) && $this->customer) {
            $contact = $this->customer->getEmail();
        }
        if (!$contact) {
            throw new LocalizedException(
                __("Unable to send email.")
            );
        }
        if (isset($this->customer) && $this->customer) {
            $this->emailSender->setCustomer($this->customer);
        }
        return $this->emailSender->sendOtp($contact, $otpInstance->getOtp(), $action);
    }

    private function sendViaMessage(string $contact, Otp $otpInstance, string $action)
    {
        if ($this->getContactType($contact) != self::CONTACT_TYPE_PHONE) {
            $contact = false;
        }
        if (isset($this->customer) && $this->customer) {
            $dialCode = '';
            $mobile = '';

            $countryRegAttr = $this->customer->getCustomAttribute('countryreg');
            if ($countryRegAttr) {
                $dialCode = $countryRegAttr->getValue();
            }

            $mobileAttr = $this->customer->getCustomAttribute('customer_mobile');
            if ($mobileAttr) {
                $mobile = $mobileAttr->getValue();
            }

            $contact = $mobile;
            if ($this->configHelper->isCountryCodeRequire()) {
                $contact = $dialCode . $mobile;
            }
        }
        if (!$contact) {
            throw new LocalizedException(
                __("Unable to send sms.")
            );
        }
        return $this->smsSender->sendOtp($contact, $otpInstance->getOtp(), $action);
    }
}
