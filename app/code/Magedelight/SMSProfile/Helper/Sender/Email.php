<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Helper\Sender;

use Magedelight\SMSProfile\Helper\ConfigHelper;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;

class Email implements OtpSenderInterface
{
    const XML_PATH_SEND_OTP = 'magedelightsmsprofile/communication/sendotp_template';

    /**
     * @var TransportBuilder
     */
    private TransportBuilder $transportBuilder;
    /**
     * @var StateInterface
     */
    private StateInterface $inlineTranslation;
    /**
     * @var ScopeConfigInterface
     */
    private ScopeConfigInterface $scopeConfig;
    /**
     * @var StoreManagerInterface
     */
    private StoreManagerInterface $storeManager;
    /**
     * @var ConfigHelper
     */
    private ConfigHelper $configHelper;
    /**
     * @var CustomerInterface
     */
    private CustomerInterface $customer;

    /**
     * Email constructor.
     * @param TransportBuilder $transportBuilder
     * @param StateInterface $inlineTranslation
     * @param ScopeConfigInterface $scopeConfig
     * @param StoreManagerInterface $storeManager
     * @param ConfigHelper $configHelper
     */
    public function __construct(
        TransportBuilder $transportBuilder,
        StateInterface $inlineTranslation,
        ScopeConfigInterface $scopeConfig,
        StoreManagerInterface $storeManager,
        ConfigHelper $configHelper
    ) {
        $this->transportBuilder = $transportBuilder;
        $this->inlineTranslation = $inlineTranslation;
        $this->scopeConfig = $scopeConfig;
        $this->storeManager = $storeManager;
        $this->configHelper = $configHelper;
    }

    /**
     * @inheritDoc
     */
    public function sendOtp($contact, $otp, $action = 'login')
    {
        $customer = $this->customer;
        $customerName = 'User';
        if (isset($customer) && $customer instanceof CustomerInterface) {
            $customerName = $customer->getFirstname() . ' ' . $customer->getLastname();
        }
        $minutes = $this->configHelper->getOTPExpiry();
        $storeDomain = $this->configHelper->getDummyEmailDomain();
        if (strpos($contact, $storeDomain) !== false) {
            if (substr($contact, 0, 1)=="+") {
                return $this;
            }
        }

        $variables = [
            'customer_name' => $customerName,
            'otp' => $otp,
            'otp_expiry' => $minutes
        ];

        $sender = [
            'name' => "OTP to Login",
            'email' => $this->scopeConfig->getValue(
                'trans_email/ident_general/email',
                ScopeInterface::SCOPE_STORE
            )
        ];

        $this->inlineTranslation->suspend();
        $templateId = $this->getEmailTemplate();
        $transport = $this->transportBuilder
            ->setTemplateIdentifier($templateId)
            ->setTemplateOptions([
                'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
                'store' => $this->storeManager->getStore()->getId()
            ])->setTemplateVars($variables)
            ->setFromByScope($sender)
            ->addTo($contact)
            ->getTransport();

        $transport->sendMessage();
        $this->inlineTranslation->resume();
        return true;
    }

    private function getEmailTemplate()
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_SEND_OTP,
            ScopeInterface::SCOPE_STORE
        );
    }

    public function setCustomer(CustomerInterface $customer)
    {
        $this->customer = $customer;
    }
}
