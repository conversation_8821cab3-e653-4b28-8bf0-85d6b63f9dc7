<?php

namespace Magedelight\SMSProfile\Plugin;

class SetExtensionAttribute
{

    /**
     * Set extension attribute
     *
     * @param \Magento\Quote\Model\Quote\Payment $subject
     * @param $result
     * @param array $data
     */
    public function afterImportData(\Magento\Quote\Model\Quote\Payment $subject, $result, array $data)
    {
        $ext = $data['extension_attributes'] ?? false;
        if ($ext && $ext->getOtp()) {
            $subject->getExtensionAttributes()->setOtp($ext->getOtp());
        }
        return $result;
    }
}
