<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SMSProfile\Model\SMSTemplates;

use Magedelight\SMSProfile\Model\ResourceModel\SMSTemplates\CollectionFactory;
use Magento\Framework\App\Request\DataPersistorInterface;

/**
 * Class DataProvider
 */
class DataProvider extends \Magento\Ui\DataProvider\AbstractDataProvider
{
    /**
     * @var \Magedelight\SMSProfile\Model\ResourceModel\SMSTemplates\CollectionFactory
     */
    protected $collection;

    /**
     * @var DataPersistorInterface
     */
    protected $dataPersistor;

    /**
     * @var array
     */
    protected $loadedData;

    protected $_request;

    /**
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $pageCollectionFactory
     * @param DataPersistorInterface $dataPersistor
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $pageCollectionFactory,
        DataPersistorInterface $dataPersistor,
        \Magento\Framework\App\RequestInterface $request,
        array $meta = [],
        array $data = []
    ) {
        $this->_request = $request;
        $this->collection = $pageCollectionFactory->create();
        $this->dataPersistor = $dataPersistor;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
        $this->meta = $this->prepareMeta($this->meta);
    }

    /**
     * Prepares Meta
     *
     * @param array $meta
     * @return array
     */
    public function prepareMeta(array $meta)
    {
        return $meta;
    }

    /**
     * Get data
     *
     * @return array
     */
    public function getData()
    {
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }

        $itemId = $this->_request->getParam('entity_id');
        if (!empty($itemId)) {
            $items = $this->collection->getItems();
            /** @var $page \Magedelight\SEOPro\Model\Page */
            foreach ($items as $crosslink) {
                $this->loadedData[$crosslink->getId()] = $crosslink->getData();
            }
        }

        $data = $this->dataPersistor->get('smstemplates');
        if (!empty($data)) {
            $crosslink = $this->collection->getNewEmptyItem();
            $crosslink->setData($data->getData());
            $this->loadedData[$crosslink->getId()] = $crosslink->getData();
            $this->dataPersistor->clear('smstemplates');
        }
        return $this->loadedData;
    }
}
