<?php

namespace Magedelight\SMSProfile\Model\Config\Source\Registration;

class OtpSetting implements \Magento\Framework\Data\OptionSourceInterface
{
    const DISABLE = 'disable';
    const BEFORE_CREATE = 'before';
    const WHILE_CREATE = 'in_form';

    /**
     * @inheritDoc
     */
    public function toOptionArray()
    {
        return [
            ['value' => self::DISABLE, 'label' => __('Not Require')],
            ['value' => self::BEFORE_CREATE, 'label' => __('Require Before Registration')],
            ['value' => self::WHILE_CREATE, 'label' => __('Require While Registration')],
        ];
    }
}
