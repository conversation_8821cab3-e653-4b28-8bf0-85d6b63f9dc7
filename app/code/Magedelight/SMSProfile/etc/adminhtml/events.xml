<?xml version="1.0" encoding="UTF-8"?>
 <!--
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <!-- <event name="admin_system_config_changed_section_magedelightsmsprofile">
        <observer name="changed_section_magedelightsmsprofile"
                  instance="Magedelight\SMSProfile\Observer\Adminhtml\CaptchaDisable"/>
    </event> -->
    <event name="admin_system_config_changed_section_magedelightsmsprofile">
        <observer name="changed_section_magedelightsmsprofile"
                  instance="Magedelight\SMSProfile\Observer\Adminhtml\ValidateSmsProfileConfig"/>
    </event>
    <event name="sales_order_invoice_save_after">
        <observer name="smsNotificationInvoice" instance="Magedelight\SMSProfile\Observer\Adminhtml\InvoicePay"/>
    </event>

    <event name="sales_order_creditmemo_save_after">
        <observer name="smsNotificationCreditmemo" instance="Magedelight\SMSProfile\Observer\Adminhtml\CreditmemoRefund"/>
    </event>

     <event name="sales_order_shipment_save_after">
        <observer name="smsNotificationShipment" instance="Magedelight\SMSProfile\Observer\Adminhtml\ShipmentSave"/>
    </event>

    <event name="order_cancel_after">
        <observer name="adminOrderCancelSms" instance="Magedelight\SMSProfile\Observer\Adminhtml\OrderCancel" />
    </event>

    <event name="sales_order_shipment_track_save_after">
        <observer name="adminOrderTrackSms" instance="Magedelight\SMSProfile\Observer\Adminhtml\ShipmentTrackSave"/>
    </event>

</config>
