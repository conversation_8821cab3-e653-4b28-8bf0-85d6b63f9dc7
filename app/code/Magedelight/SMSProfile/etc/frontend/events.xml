<?xml version="1.0" encoding="UTF-8"  ?>
<!--
/**
 * Magedelight
 * Copyright (C) 2019 Magedelight <<EMAIL>>
 *
 * @category Magedelight
 * @package Magedelight_SMSProfile
 * @copyright Copyright (c) 2019 Mage Delight (http://www.magedelight.com/)
 * @license http://opensource.org/licenses/gpl-3.0.html GNU General Public License,version 3 (GPL-3.0)
 * <AUTHOR> <<EMAIL>>
 */
-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="customer_save_after_data_object">
       <observer name="magedelight_smsprofile_customer_save" instance="Magedelight\SMSProfile\Observer\CustomerSaveAfter" />
    </event>

    <event name="controller_action_predispatch">
      <observer name="magedelight_smsprofile_controller_action_predispatch" instance="Magedelight\SMSProfile\Observer\ControllerActionPredispatch" shared="false" />
  	</event>
    <event name="layout_load_before">
        <observer name="add-custom-handle" instance="Magedelight\SMSProfile\Observer\AddCustomHandle"/>
    </event>
    <event name="customer_customer_authenticated">
        <observer name="reset-captcha-for-mobile" instance="Magedelight\SMSProfile\Observer\ResetCaptcha"/>
    </event>
    <event name="view_block_abstract_to_html_after">
        <observer name="add_route" instance="Magedelight\SMSProfile\Observer\ConfigBlock"/>
    </event>
</config>
