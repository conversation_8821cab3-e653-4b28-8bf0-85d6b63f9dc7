<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

/** @var \Magento\Customer\Block\Account\AuthenticationPopup $block */
/** @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer */
/** @var Magento\Customer\ViewModel\Customer\StoreConfig $viewModel */
$viewModel = $block->getViewModel();
?>
<div id="mdAuthenticationPopup" data-bind="scope:'mdAuthenticationPopup'">
    <?php $scriptString = 'window.authenticationPopup = ' . /* @noEscape */ $block->getSerializedConfig(); ?>
    <?= /* @noEscape */ $secureRenderer->renderTag('script', [], $scriptString, false); ?>
    <!-- ko template: getTemplate() --><!-- /ko -->
    <?php // phpcs:ignore Magento2.Legacy.PhtmlTemplate ?>
    <script type="text/x-magento-init">
        {
            "#mdAuthenticationPopup": {
                "Magento_Ui/js/core/app": <?= /* @noEscape */ $block->getJsLayout() ?>
            },
            "*": {
                "Magento_Ui/js/block-loader": "<?= $block->escapeJs($block->escapeUrl($block->getViewFileUrl(
                    'images/loader-1.gif'
                ))) ?>"
                <?php if ($viewModel->isGlobalScopeEnabled()): ?>
                ,
                "Magento_Customer/js/customer-global-session-loader": {}
                <?php endif; ?>
            }
        }
    </script>
</div>
