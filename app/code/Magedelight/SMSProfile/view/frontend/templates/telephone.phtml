<?php
/**
 * @package Magedelight_SMSProfile for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

/** @var \Magento\Customer\Block\Widget\Telephone $block */
?>
<?php
$smsProfileHelper = $this->helper('Magedelight\SMSProfile\Helper\Data');
?>
<div class="field telephone <?= $block->isRequired() ? 'required' : '' ?>">
    <label for="telephone" class="label">
        <span>
            <?= $block->escapeHtml(__('Phone Number')) ?>
        </span>
    </label>
    <div class="control">
        <input type="tel"
               name="telephone"
               id="telephone"
               value="<?= $block->escapeHtmlAttr($block->getTelephone()) ?>"
               title="<?= $block->escapeHtmlAttr(__('Phone Number')) ?>"
               class="input-text <?= $block->escapeHtmlAttr(
                   $block->getAttributeValidationClass('telephone')
               )?>"
             data-validate="{'validate-number':true}"
        >
        <?php if($smsProfileHelper->isCustomerCountryEnabled()): ?>
          <input type="hidden" name='login[countrycodeval]' value="" id="countryreg" name="countryreg" />
          <input type="hidden" value="" id="countryregcode" name="countryregcode" />
        <?php endif; ?>
    </div>
</div>
<?php if($smsProfileHelper->isCustomerCountryEnabled()): ?>
  <script type="text/x-magento-init">
    {
      "input[name='telephone'": {
      "Magedelight_SMSProfile/js/intlTelephoneInput":
      {}
    }
  }
</script>
<?php endif; ?>
