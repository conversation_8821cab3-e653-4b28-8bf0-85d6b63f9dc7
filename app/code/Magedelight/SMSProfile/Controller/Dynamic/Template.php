<?php

namespace Magedelight\SMSProfile\Controller\Dynamic;


use Magento\Framework\App\RequestInterface;
use Magento\Framework\Controller\Result\RawFactory;
use Magento\Framework\View\Result\PageFactory;

class Template implements \Magento\Framework\App\Action\HttpGetActionInterface
{
    /**
     * @var RawFactory
     */
    private RawFactory $resultFactory;
    /**
     * @var PageFactory
     */
    private PageFactory $pageFactory;
    /**
     * @var RequestInterface
     */
    private RequestInterface $request;

    /**
     * Template constructor.
     * @param RawFactory $resultFactory
     * @param PageFactory $pageFactory
     * @param RequestInterface $request
     */
    public function __construct(
        RawFactory $resultFactory,
        PageFactory $pageFactory,
        RequestInterface $request
    ) {
        $this->resultFactory = $resultFactory;
        $this->pageFactory = $pageFactory;
        $this->request = $request;
    }

    /**
     * @inheritDoc
     */
    public function execute()
    {
        $action = $this->request->getParam('action');
        $page = $this->pageFactory->create();
        $layout = $page->getLayout();
        $result = $this->resultFactory->create();
        switch ($action) {
            case 'create':
                $layout->getUpdate()
                    ->addHandle('customer_account_create')
                    ->addHandle('update_create');
                $content = $layout->getBlock('customer_form_register');
                $result->setContents($content->toHtml());
                break;
            case 'forgot':
                $layout->getUpdate()
                    ->addHandle('default')
                    ->addHandle('customer_account_forgotpassword')
                ->addHandle('update_forgotpassword');
                $b = $layout->getBlock('forgot-parent');
                $result->setContents($b->toHtml());
                break;
        }
        return $result;
    }
}
