<?php
/**
 * @package Magedelight_Storepickup for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Storepickup\Observer;

use Magento\Framework\App\RequestInterface;
use Magento\Framework\Event\Observer as EventObserver;
use Magento\Framework\Event\ObserverInterface;
use Magedelight\Storepickup\Model\Source\Region;

class SaveOrderEdit implements ObserverInterface
{

    /**
     * @var RequestInterface
     */
    protected $_request;

    /**
     * @var RequestInterface
     */
    protected $storeInformationManagementInterface;

    /**
     * @var Storedetails
     */
    protected $storeDetails;

    /**
     * @var Region
     */
    protected $regionOptions;

    /**
     * @param RequestInterface $request
     * @param Magedelight\Storepickup\Api\StoreInformationManagementInterface $storeInformationManagementInterface
     * @param Region $regionOptions
     */
    public function __construct(
        RequestInterface $request,
        \Magedelight\Storepickup\Api\StoreInformationManagementInterface $storeInformationManagementInterface,
        Region $regionOptions
    ) {
        $this->_request = $request;
        $this->storeInformationManagementInterface = $storeInformationManagementInterface;
        $this->regionOptions = $regionOptions;
    }

    /**
     * Set storepickup form
     *
     * @param EventObserver $observer
     * @return SaveOrderEdit
     */
    public function execute(EventObserver $observer)
    {
        $quote = $observer->getQuote();
        $order = $observer->getOrder();

        $storeId = $this->_request->getPost('storepickup_storename');

        if (isset($storeId) && ! empty($storeId)) {

            $this->storeDetails = $this->getStoreInformation($storeId);
            $storePickupData = $this->getStorePickupAddress();
            $date = $this->_request->getPost('storepickup_pickup_date');
            $time = $this->_request->getPost('storepickup_store_time');
            $storeName = $this->storeDetails[0]['storename'];
            
            $order->setPickupStore($storeName);
            $order->setPickupDate($date.$time);
            $order->getShippingAddress()->addData($storePickupData);
            $order->setPickupStoreEmail($this->storeDetails[0]['storeemail']);

            $quote->setPickupStore($storeName);
            $quote->setPickupDate($date.$time);
            $quote->setPickupStoreEmail($this->storeDetails[0]['storeemail']);

            $order->save();
        }
        return $this;
    }

    /**
     * Get store details
     *
     * @param int $storeId
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function getStoreInformation($storeId)
    {
        $storeDetails = $this->storeInformationManagementInterface->getStoreInformationById($storeId);
        return $storeDetails;
    }

    /**
     * Get store address
     *
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getStorePickupAddress()
    {
        $fields = ['storename', 'address', 'city', 'region', 'region_id', 'country_id', 'zipcode', 'telephone'];
        $storelocatorAddress = [
            'firstname' => $this->storeDetails[0]['storename'],
            'lastname' => $this->storeDetails[0]['storename'],
            'street' => str_replace('\n', ', ', $this->storeDetails[0]['address']),
            'city' => $this->storeDetails[0]['city'],
            'country_id' => $this->storeDetails[0]['country_id'],
            'region' => $this->getRegionName($this->storeDetails[0]['region_id']),
            'region_id' => $this->storeDetails[0]['region_id'],
            'postcode' => $this->storeDetails[0]['zipcode'],
            'telephone' => implode(',', $this->storeDetails[0]['telephone'] ?? []),
            'fax' => '',
            'save_in_address_book' => 1,
        ];
        return $storelocatorAddress;
    }

    /**
     * Get region
     *
     * @param int $region_id
     * @return mixed
     */
    public function getRegionName($region_id)
    {
        $regionArray = $this->regionOptions->getOptions();
        return $regionArray[$region_id];
    }
}
