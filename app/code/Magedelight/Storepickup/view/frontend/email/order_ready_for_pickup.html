<!--
/**
* @package Magedelight_Storepickup for Magento 2
* <AUTHOR> Team
* @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
*/
-->
<!--@subject {{trans "Your %store_name order is ready for pickup" store_name=$store.getFrontendName()}} @-->
<!--@vars {
"var formattedBillingAddress|raw":"Billing Address",
"var order.getEmailCustomerNote()":"Email Order Note",
"var order.increment_id":"Order Id",
"layout handle=\"sales_email_order_items\" order=$order area=\"frontend\"":"Order Items Grid",
"var payment_html|raw":"Payment Details",
"var formattedShippingAddress|raw":"Shipping Address",
"var order.getShippingDescription()":"Shipping Description",
"var shipping_msg":"Shipping message",
"var display_delivery_pin":"Display Delivery Pin",
"var enable_delivery_pin":"Enable Delivery Pin",
"var order.getIsNotVirtual()":"Order Virtual",
"var store.getFrontendName()":"Frontend Name",
"var order.getCustomerName()":"Customer Name",
"var order.getDeliveryPin()":"Order Delivery Pin",
"var order.getEmailCustomerNote()":"Customer Note",
"var order.getEmailCustomerNote()|escape|nl2br":"Customer Note",
"var order":"Order"
} @-->

{{template config_path="design/email/header_template"}}

<table>
    <tr class="email-intro">
        <td>
            <p class="greeting">{{var order.getCustomerName()}}</p>
            <p>
               {{trans "Your Order is ready for pickup from %store_name." store_name=$store.getFrontendName()}}
            </p>
            {{depend enable_delivery_pin}}
                <p>{{trans 'Please verify delivery pin while pickup your order.' }} </p>
                {{if display_delivery_pin}}
                    <p><strong>{{trans 'Delivery Pin:' }} {{var order.getDeliveryPin()}}</strong></p>
                {{else}}
                    <p><strong>{{trans 'Please find your pin from my account'}}</strong></p>
                {{/if}}
            {{/depend}}
        </td>
    </tr>

    <tr class="email-summary">
        <td>
            <h1>{{trans 'Order Details:' }}</h1>
        </td>
    </tr>

    <tr class="email-information">
        <td>
            {{depend order.getEmailCustomerNote()}}
            <table class="message-info">
                <tr>
                    <td>
                        {{var order.getEmailCustomerNote()|escape|nl2br}}
                    </td>
                </tr>
            </table>
            {{/depend}}
            <table class="order-details">
                <tr>
                    <td class="address-details">
                        <h3>{{trans "Billing Info"}}</h3>
                        <p>{{var formattedBillingAddress|raw}}</p>
                    </td>
                    {{depend order.getIsNotVirtual()}}
                    <td class="address-details">
                        <h3>{{trans "Shipping Info"}}</h3>
                        <p>{{var formattedShippingAddress|raw}}</p>
                    </td>
                    {{/depend}}
                </tr>
                <tr>
                    <td class="method-info">
                        <h3>{{trans "Payment Method"}}</h3>
                        {{var payment_html|raw}}
                    </td>
                    {{depend order.getIsNotVirtual()}}
                    <td class="method-info">
                        <h3>{{trans "Shipping Method"}}</h3>
                        <p>{{var order.getShippingDescription()}}</p>
                        {{if shipping_msg}}
                        <p>{{var shipping_msg}}</p>
                        {{/if}}
                    </td>
                    {{/depend}}
                </tr>
            </table>
            {{layout handle="sales_email_order_items" order=$order area="frontend"}}
        </td>
    </tr>
</table>

{{template config_path="design/email/footer_template"}}
