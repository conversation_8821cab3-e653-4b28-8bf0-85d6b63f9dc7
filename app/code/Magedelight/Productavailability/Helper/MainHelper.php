<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Helper;

use Magento\Framework\App\Helper\Context;
use Magento\Framework\View\LayoutInterface;
use Magento\Framework\Stdlib\CookieManagerInterface;
use Magento\Framework\ObjectManagerInterface;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Checkout\Model\Cart;
use Magedelight\Productavailability\Model\ProductRule;
use Magento\Store\Model\StoreManager;
use Magedelight\Productavailability\Model\PostcodeRepository;
use Magedelight\Productavailability\Helper\Data;

class MainHelper extends AbstractHelper
{
    
    /**
     * @var CookieManagerInterface
     */
    private $cookieManager;
    
    /**
     * @var LayoutInterface
     */
    private $layout;
    
    /**
     * @var ObjectManagerInterface
     */
    private $objectManager;

    /**
     * @var Cart
     */
    private $cart;
    
    /**
     * @var ProductRule
     */
    private $postcodeProductRule;
    
    /**
     * @var PostcodeRepository
     */
    private $postcodeRepository;
    
    /**
     * @var Data
     */
    private $helper;

    /**
     * @var Magento\Store\Model\StoreManager
     */
    private $storeManager;
    /**
     *
     * @param Context $context
     * @param CookieManagerInterface $cookieManager
     * @param LayoutInterface $layout
     * @param ObjectManagerInterface $objectmanager
     * @param Cart $cart
     * @param ProductRule $postcodeProductRule
     * @param StoreManager $storeManager
     * @param PostcodeRepository $postcodeRepository
     * @param Data $helper
     */
    public function __construct(
        Context $context,
        CookieManagerInterface $cookieManager,
        LayoutInterface $layout,
        ObjectManagerInterface $objectmanager,
        Cart $cart,
        ProductRule $postcodeProductRule,
        StoreManager $storeManager,
        PostcodeRepository $postcodeRepository,
        Data $helper
    ) {
        $this->cookieManager = $cookieManager;
        $this->layout = $layout;
        $this->objectManager = $objectmanager;
        $this->cart = $cart;
        $this->postcodeProductRule = $postcodeProductRule;
        $this->storeManager = $storeManager;
        $this->postcodeRepository = $postcodeRepository;
        $this->helper = $helper;
        
        parent::__construct($context);
    }
    
    /**
     * Get Quote
     *
     * @return \Magento\Quote\Model\Quote
     */
    private function getQuote()
    {
        return $this->cart->getQuote();
    }
    
    /**
     * Retrieve array of cart product ids
     *
     * @return array
     */
    public function getCartProductId()
    {
        $productIds = [];
        
        $ids = $this->cart->getProductIds();
        if (!empty($ids)) {
            foreach ($ids as $id) {
                $productIds[] = $this->postcodeRepository->getCurrentProductId($id);
            }
        }
        
        return $productIds;
    }
    
    /**
     * Get Shipping Post Code
     *
     * @return mixed postcode
     */
    public function getShippingPostcode()
    {
        return $this->getQuote()->getShippingAddress()->getPostcode();
    }
    
    /**
     * Get Restricted Data
     *
     * @param type $postcode
     * @param array $productIds
     * @param string $type
     * @return array $result
     */
    public function getRestrictedData($postcode, $productIds = [], $type = null)
    {
        $result = [];
        $resultString = '';
        
        $storeId = $this->getStoreId();
        $getColumn = ($type == 'payment_methods') ? 'payment_methods' : 'shipping_methods';
        
        if ($postcode && $productIds) {
            foreach ($productIds as $productId) {
                $data = $this->postcodeProductRule->getRestrictedData($productId, $storeId, $postcode, $getColumn);
                
                if ($data && isset($data[$getColumn])) {
                    $resultString =
                        ($resultString)
                        ? $resultString . "," . $data[$getColumn]
                        : $data[$getColumn];
                }
            }
        }
        
        if ($resultString) {
            $result = array_unique(explode(',', $resultString));
        }
        
        return $result;
    }
    
    /**
     * Check Product has Zip Code or Not
     *
     * @param string $postcode
     * @param array $productIds
     * @return array $result
     */
    public function isProductHasZipcode($postcode, $productIds = [])
    {
        $result = true;
        $storeId = $this->getStoreId();

        if ($postcode && $productIds) {
            foreach ($productIds as $productId) {
                $data = $this->postcodeProductRule->getRestrictedData($productId, $storeId, $postcode, 'postcode_id');
                if (!$data) {
                    $result = false;
                    break;
                }
            }
        }

        return $result;
    }
    
    /**
     * Check Module is Enabled or Not
     */
    public function isModuleEnable()
    {
        $storeId = $this->getStoreId();
        return $this->helper->isModuleEnable($storeId);
    }
    
    /**
     * Get Store Id
     */
    public function getStoreId()
    {
        return $this->storeManager->getStore()->getId();
    }
}
