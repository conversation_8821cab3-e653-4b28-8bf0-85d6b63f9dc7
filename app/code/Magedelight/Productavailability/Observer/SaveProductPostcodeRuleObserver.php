<?php

/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Observer;

use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\Event\Observer as EventObserver;
use Magedelight\Productavailability\Model\ProductRule;
use Magedelight\Productavailability\Model\PostcodeRepository;
use Magedelight\Productavailability\Helper\Data;
use Magento\Framework\App\RequestInterface;

class SaveProductPostcodeRuleObserver implements ObserverInterface
{
    /**
     * @var \Magedelight\Productavailability\Model\ProductRule
     */
    private $productRule;
    
    /**
     * @var \Magedelight\Productavailability\Model\PostcodeRepository
     */
    private $postcodeRepository;
    
    /**
     * @var \Magedelight\Productavailability\Helper\Data
     */
    private $helper;
    
    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    private $request;

    /**
     * SavePostCodeRuleRelation constructor
     * @param \Magento\Framework\App\RequestInterface $request
     * @param \Magedelight\Productavailability\Model\ProductRule $productRule
     * @param \Magedelight\Productavailability\Model\PostcodeRepository $postcodeRepository
     * @param \Magedelight\Productavailability\Helper\Data $helper
     */
    public function __construct(
        RequestInterface $request,
        ProductRule $productRule,
        PostcodeRepository $postcodeRepository,
        Data $helper
    ) {
        $this->request = $request;
        $this->productRule = $productRule;
        $this->postcodeRepository = $postcodeRepository;
        $this->helper = $helper;
    }

    /**
     * Save PostcodeRule On Product Save
     *
     * @param EventObserver $observer
     * @return $this
     * @throws CouldNotSaveException
     */
    public function execute(EventObserver $observer)
    {
        $storeId = $this->request->getParam('store', 0);
        if (!$this->helper->isModuleEnable($storeId)) {
            return $this;
        }
        
        $productId = $observer->getEvent()->getProduct()->getId();
        $postcodeRuleID = $observer->getEvent()->getProduct()->getPostcoderuleId();
        
        // $productId = $this->postcodeRepository->getCurrentProductId($currentProductId);
        try {
            $this->productRule->saveProductRuleData($productId, $postcodeRuleID, $storeId);
            $this->postcodeRepository->sendCustomerEmail($productId, $postcodeRuleID, $storeId);
        } catch (\Exception $ex) {
            throw new CouldNotSaveException(__('Could not save the Product Rule: %1', $ex->getMessage()));
        }

        return $this;
    }
}
