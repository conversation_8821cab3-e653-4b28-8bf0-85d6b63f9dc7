<?php

/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Ui\DataProvider\Product\Form\Modifier;

use Magento\Catalog\Model\Locator\LocatorInterface;
use Magedelight\Productavailability\Model\ProductRule;

/**
 * Data provider for advanced inventory form
 */
class PostcodeRule extends \Magento\Catalog\Ui\DataProvider\Product\Form\Modifier\AbstractModifier
{
    public const POSTCODE_FIELD = 'postcoderule_id';

    /**
     * @var LocatorInterface
     */
    private $locator;
    
    /**
     * @var ProductRule
     */
    private $productRuleModel;
    
    /**
     * @var array
     */
    private $meta = [];
    
    /**
     * Constructor
     *
     * @param LocatorInterface $locator
     * @param ProductRule $productRule
     */
    public function __construct(
        LocatorInterface $locator,
        ProductRule $productRule
    ) {
        $this->locator = $locator;
        $this->productRuleModel = $productRule;
    }
    
    /**
     * Modify Meta Function
     *
     * @param array $meta
     */
    public function modifyMeta(array $meta)
    {
        $this->meta = $meta;
        return $this->meta;
    }

    /**
     * Modify Data Function
     *
     * @param array $data
     */
    public function modifyData(array $data)
    {
        $model = $this->locator->getProduct();
        $modelId = $model->getId();
        $storeId = $model->getStoreId();
        
        $ruleId = $this->productRuleModel->getPostcodeRuleId($modelId, $storeId);
        if ($ruleId) {
            $data[$modelId][self::DATA_SOURCE_DEFAULT][self::POSTCODE_FIELD] = $ruleId;
        }
        
        return $data;
    }
}
