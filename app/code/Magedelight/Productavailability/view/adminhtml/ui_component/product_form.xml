<?xml version="1.0" ?>
<!--
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
-->
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="postcoderule">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="label" xsi:type="string" translate="true">Select Postcode/Zipcode Rule</item>
                <item name="sortOrder" xsi:type="number">20</item>
                <item name="collapsible" xsi:type="boolean">true</item>
            </item>
        </argument>
        <field name="postcode_rule">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Magedelight\Productavailability\Ui\Component\Product\PostcodeRule\Options</item>
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Zipcode/Postcode Rule</item>
                    <item name="componentType" xsi:type="string">field</item>
                    <item name="formElement" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Catalog/js/components/new-category</item>
                    <item name="elementTmpl" xsi:type="string">ui/grid/filters/elements/ui-select</item>
                    <item name="dataScope" xsi:type="string">data.product.postcoderule_id</item>
                    <item name="filterOptions" xsi:type="boolean">true</item>
                    <item name="showCheckbox" xsi:type="boolean">false</item>
                    <item name="disableLabel" xsi:type="boolean">true</item>
                    <item name="multiple" xsi:type="boolean">false</item>
                    <item name="levelsVisibility" xsi:type="number">1</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                    <item name="required" xsi:type="boolean">true</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                    <item name="listens" xsi:type="array">
                        <item name="${ $.namespace }.${ $.namespace }:responseData" xsi:type="string">setParsed</item>
                    </item>
                </item>
            </argument>
        </field>
    </fieldset>
</form>