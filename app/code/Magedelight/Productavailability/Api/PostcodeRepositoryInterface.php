<?php

/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */

namespace Magedelight\Productavailability\Api;

interface PostcodeRepositoryInterface
{
    /**
     * Save postcode.
     *
     * @param \Magedelight\Productavailability\Api\Data\PostcodeInterface $postcode
     * @return \Magedelight\Productavailability\Api\Data\PostcodeInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(Data\PostcodeInterface $postcode);

    /**
     * Retrieve postcode.
     *
     * @param int $postcodeId
     * @return \Magedelight\Productavailability\Api\Data\PostcodeInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getById($postcodeId);

    /**
     * Get custom option for a specific product
     *
     * @param string $postcode
     * @param int $productId
     * @param int $customerId
     * @return \Magedelight\Productavailability\Api\Data\PostcodeInterface
     */
    public function get($postcode, $productId, $customerId = null);
}
