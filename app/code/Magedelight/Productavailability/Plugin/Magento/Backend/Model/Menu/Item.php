<?php
/**
 * @package Magedelight_Productavailability for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
namespace Magedelight\Productavailability\Plugin\Magento\Backend\Model\Menu;

class Item
{
    /**
     * Get Doumentation URL
     *
     * @param mixed $subject
     * @param mixed $result
     */

    public function afterGetUrl($subject, $result)
    {
        $menuId = $subject->getId();
        
        if ($menuId == 'Magedelight_Productavailability::documentation') {
            $result = 'http://docs.magedelight.com/display/MAG/Delivery+Availability+Checker+-+Magento+2';
        }
        return $result;
    }
}
