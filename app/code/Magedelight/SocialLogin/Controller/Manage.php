<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON>h TechnoLabs. All Rights reserved.
 */
namespace Magedelight\SocialLogin\Controller;

use Magento\Framework\App\RequestInterface;
use Magedelight\SocialLogin\Model\SocialMedia as Social;
use Magento\Framework\Message\ManagerInterface as MessageManager;
use Magento\Store\Model\StoreManagerInterface as StoreManager;

/**
 * Customers newsletter subscription controller
 */
abstract class Manage extends \Magento\Framework\App\Action\Action
{
    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $_customerSession;
    protected $sessionManager;
    protected $apiObject;
    protected $messageManager;
    protected $storeManager;

    /**
     * Construct
     *
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Customer\Model\Session $customerSession
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Framework\Session\SessionManagerInterface $sessionManager,
        Social $apiObject,
        MessageManager $messageManager,
        StoreManager $storeManager
    ) {
        parent::__construct($context);
        $this->_customerSession = $customerSession;
        $this->sessionManager = $sessionManager;
        $this->apiObject = $apiObject;
        $this->messageManager = $messageManager;
        $this->storeManager = $storeManager;
    }

    /**
     * Check customer authentication for some actions
     *
     * @param RequestInterface $request
     * @return \Magento\Framework\App\ResponseInterface
     */
    public function dispatch(RequestInterface $request)
    {
        
        if (!$this->_customerSession->authenticate()) {
            $this->_actionFlag->set('', 'no-dispatch', true);
        }

        $this->sessionManager->start();
        $type = $this->sessionManager->getType();
        if ($type) {
            try {
                $userProfile = $this->apiObject->getUserProfile($type);
                if ($userProfile instanceof \Hybridauth\User\Profile) {
                    // Check if social_id already exists
                    $existingSocial = $this->apiObject->getCollection()
                        ->addFieldToFilter('social_id', $userProfile->identifier)
                        ->addFieldToFilter('type', $type)
                        ->getFirstItem();

                    if ($existingSocial && $existingSocial->getId()) {
                        // Social ID already exists, just unset type and continue
                        $this->sessionManager->unsType();
                        return parent::dispatch($request);
                    }

                    // Social ID doesn't exist, proceed with customer creation/linking
                    $customer = $this->createCustomerProcess($userProfile, $type);
                    if ($customer && $customer->getId()) {
                        $this->sessionManager->unsType();
                    } else {
                        return $this->_redirect('*/*/login');
                    }
                }
            } catch (\Exception $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            }
        }
        return parent::dispatch($request);
    }

    /**
     * CreateCustomerProcess
     *
     * @param UserProfile $userProfile
     * @param Type $type
     *
     * @return bool|Customer|mixed
     * @throws Exception
     * @throws LocalizedException
     */
    protected function createCustomerProcess($userProfile, $type)
    {
        $name = explode(' ', $userProfile->displayName ?: __('New User'));

        $user = array_merge(
            [
                'email' => $userProfile->email ?: $userProfile->identifier . '@' . strtolower($type) . '.com',
                'firstname' => $userProfile->firstName ?: (array_shift($name) ?: $userProfile->identifier),
                'lastname' => $userProfile->lastName ?: (array_shift($name) ?: $userProfile->identifier),
                'identifier' => $userProfile->identifier,
                'type' => $type,
                'password' => $userProfile->password ?? null,
                'displayname' => $userProfile->displayName
            ],
            $this->getUserData($userProfile)
        );

        return $this->createCustomer($user, $type);
    }

    /**
     * Create customer from social data
     *
     * @param User $user
     * @param Type $type
     *
     * @return bool|Customer|mixed
     * @throws Exception
     * @throws LocalizedException
     */
    protected function createCustomer($user, $type)
    {
        // Check if social_id already exists
        $existingSocial = $this->apiObject->getCollection()
            ->addFieldToFilter('social_id', $user['identifier'])
            ->addFieldToFilter('type', $type)
            ->getFirstItem();

        if ($existingSocial && $existingSocial->getId()) {
            // Social ID already exists, return existing customer
            return $this->customerFactory->create()->load($existingSocial->getCustomerId());
        }

        // Check by email
        $customer = $this->apiObject->getCustomerByEmail($user['email'], $this->storeManager->getStore()->getWebsiteId());
        if ($customer->getId()) {
            $this->apiObject->setAuthorCustomer($user, $customer->getId(), $type);
        } else {
            try {
                $customer = $this->apiObject->createCustomerSocial($user, $this->storeManager->getStore());
            } catch (\Exception $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
                return false;
            }
        }

        return $customer;
    }

    /**
     * GetUserData
     *
     * @param Profile $profile
     *
     * @return array
     */
    protected function getUserData($userProfile)
    {
        return [];
    }
}
