<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
namespace Magedelight\SocialLogin\Controller\Adminhtml\Report\Sociallogin;

use Magento\Framework\App\ResponseInterface;
use Magento\Framework\App\Filesystem\DirectoryList;

class ExportSocialloginCsv extends \Magento\Reports\Controller\Adminhtml\Report\Sales
{
    /**
     * Export sales report by category grid to CSV format
     *
     * @return ResponseInterface
     * @throws \Exception
     */
    public function execute()
    {
        $fileName = 'social_login.csv';
        $grid = $this->_view->getLayout()
            ->createBlock(\Magedelight\SocialLogin\Block\Adminhtml\SocialLogin\SocialLogin\Grid::class);
        $this->_initReportAction($grid);
        return $this->_fileFactory->create($fileName, $grid->getCsvFile(), DirectoryList::VAR_DIR);
    }
}
