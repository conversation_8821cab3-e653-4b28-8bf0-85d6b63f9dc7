<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */

namespace Magedelight\SocialLogin\Model\Config\Source;

class Direction implements \Magento\Framework\Option\ArrayInterface
{
    /**
     * Return options array.
     *
     * @return array
     */
    public function toOptionArray()
    {
        return [
                ['value' => 'current', 'label' => __('Current Page')],
                ['value' => 'myaccount', 'label' => __('My Account')],
                ['value' => 'homepage', 'label' => __('Homepage')],
                ['value' => 'shoppingcart', 'label' => __('Shopping Cart')],
        ];
    }
}
