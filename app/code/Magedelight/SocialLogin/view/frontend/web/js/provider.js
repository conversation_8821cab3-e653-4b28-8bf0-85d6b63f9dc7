/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON>no<PERSON>abs. All Rights reserved.
 */

define([
    'jquery',
    'Magento_Customer/js/customer-data'
], function ($, customerData) {
    'use strict';

    window.socialCallback = function (url, windowObj) {
        if (url !== '') {
                window.location.href = url;
        } else {
            window.location.reload();
        }
            windowObj.close();
    };

    return function (config, element) {
        var model = {
            initialize: function () {
                var self = this;
                $(element).on('click', function (e) {
                    e.preventDefault();
                    self.openPopup();
                });
            },

            openPopup: function () {
                window.open(config.url, config.label, this.getPopupParams());
            },

            getPopupParams: function () {
                var w = 600,
                    h = 500;

                const dualScreenLeft = window.screenLeft !== undefined ? window.screenLeft : window.screenX;
                const dualScreenTop = window.screenTop !== undefined ? window.screenTop : window.screenY;

                const width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
                const height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

                const systemZoom = width / window.screen.availWidth;
                var left = (width - w) / 2 / systemZoom + dualScreenLeft / 2
                const top = (height - h) / 2 / systemZoom + dualScreenTop / 2

                if (dualScreenLeft < 0) {
                    left = -(-dualScreenLeft / 2 + w);
                }

                return (
                    'width=' + w +
                    ',height=' + h +
                    ',left=' + left +
                    ',top=' + top
                );
            }
        };
        model.initialize();

        return model;
    };
});
