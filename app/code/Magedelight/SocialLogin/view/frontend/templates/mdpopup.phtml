<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by <PERSON><PERSON> TechnoLabs. All Rights reserved.
 */
?>
<?php /** @var \Magedelight\SocialLogin\Block\MdPopup $block */
$currentUrl = $block->getUrl('*/*/*', ['_current' => true, '_use_rewrite' => true]);
$myLastElement='';
$getIdentifier=explode('/', $currentUrl);
if (str_contains($currentUrl, 'checkout')) {
    $cnt=count($getIdentifier);
    $myLastElement = $getIdentifier[$cnt-2];
}

if ($block->isLoginPopupEnable() && $block->isModuleEnabled() &&
    !str_contains($currentUrl, 'customer/account/create/') &&
    !str_contains($currentUrl, 'customer/account/login/') &&
    !str_contains($currentUrl, 'customer/account/forgotpassword/') && $myLastElement!='checkout'): ?>
    <?php $position = $block->getDisplayPosition();?>
    <?php
    $enabled_custom_style = $block->getIsenabledCustomStyle();
    $config_color = '#'.$block->getConfigColor();
    $config_font_color = '#'.$block->getConfigFontColor();
    $customStyle = $block->getConfigCss();
    ?>
    <?php if ($enabled_custom_style): ?>
        <!-- backend configuration color  -->
        <?php if ($config_font_color!="") {
            // @codingStandardsIgnoreStart
         /* @noEscape */ $secureRenderer->renderTag('style', [], ".modal-inner-wrap.md-social-popup .primary button,.modal-inner-wrap.md-social-popup #md-register-content .primary button,.modal-inner-wrap.md-social-popup #md-forgot-content .primary button{background:$config_color; border:$config_color;color:$config_font_color}", false);
            // @codingStandardsIgnoreEnd
        }?>
        <?php if ($customStyle!="") {
            /* @noEscape */ $secureRenderer->renderTag('style', [], "$customStyle", false);
        }?>
    <?php endif;?>
    <div class="md-login-container" data-md-js="md-login-container" style="display: none">
        <div id="md-login-popup"
             class="md-login-popup <?= /* @noEscape */ $position ?>"
             data-md-js="md-tab-container"
             data-mage-init='{"tabs":{"openedState":"active"}}'
             data-position="<?= /* @noEscape */ $position ?>">
            <div class="md-tabs-wrapper" data-md-js="md-tabs-wrapper">
                <ul class="md-tablist">
                    <li data-role="collapsible" class="md-title" data-md-js="md-popup-tab">
                        <a href="#md-login-content"
                           class="md-link"
                           data-toggle="switch"
                           tabindex="0"><?= $block->escapeHtml(__('Login')) ?></a>
                    </li>
                    <li data-role="collapsible" class="md-title" data-md-js="md-popup-tab">
                        <a href="#md-register-content"
                           class="md-link"
                           data-toggle="switch"
                           tabindex="1"><?= $block->escapeHtml(__('Register')) ?></a>
                    </li>
                </ul>
                <div class="md-error" style="color: red;"><span></span></div>
                <div class="md-success" style="color: green;"><span></span></div>
                <?= /* @noEscape */ $block->getChildHtml('md.customer.sociallogin'); ?>
                <div id="md-login-content" class="md-content md-login-content" data-role="content">
                    <?= $block->getChildHtml('md_customer_form_login'); ?>
                </div>
                <div id="md-register-content" class="md-content md-register-content" data-role="content">
                    <?= $block->getChildHtml('md_customer_form_register'); ?>
                </div>
            </div>
            <div class="md-tabs-wrapper -forgot" data-md-js="md-tabs-wrapper-forgot">
                <ul class="md-tablist">
                    <li class="md-title active">
                        <a href="#md-forgot-content" class="md-link">
                            <?= $block->escapeHtml(__('Forgot Your Password?')) ?>
                        </a>
                    </li>
                </ul>
                <div class="md-error" style="color: red;"><span></span></div>
                <div class="md-success" style="color: green;"><span></span></div>
                <div id="md-forgot-content" class="md-content md-forgot-content">
                    <?= $block->getChildHtml('md_forgotPassword'); ?>
                </div>
            </div>
        </div>
        <script type="text/x-magento-init">
        {
            "[data-md-js='md-login-container']" : {
                "Magedelight_SocialLogin/js/md-popup":{}
            }
        }

        </script>
    </div>
<?php endif; ?>
