<?php
/**
 * @package Magedelight_SocialLogin for Magento 2
 * <AUTHOR> Team
 * @copyright Copyright (c) MageDelight (https://www.magedelight.com) owned by Krish TechnoLabs. All Rights reserved.
 */
?>
<div class="dashboard-container mdssp-dashboard-container row">
    <div class="dashboard-main mdssp-dashboard-main col-m-12">
        <div class="dashboard-diagram-container mdssp-dashboard-diagram-container">
            <div id="diagram_tab" class="ui-tabs ui-widget ui-widget-content ui-corner-all">
                <ul class="tabs-horiz ui-tabs-nav ui-helper-reset ui-helper-clearfix ui-widget-header ui-corner-all"
                    role="tablist">
                    <li class="ui-state-default ui-corner-top ui-tabs-active ui-state-active">
                        <a href="#diagram_tab_orders_content"
                           title="Orders"
                           class="ui-tabs-anchor"
                           data-graph="subscription">
                            <span><?= /* @noEscape */ __('Date wise Providers ') ?></span>
                        </a>
                    </li>
                    <li class="ui-state-default ui-corner-top">
                        <a href="#diagram_tab_orders_content"
                           title="Social Providers"
                           class="ui-tabs-anchor"
                           data-graph="socials">
                            <span><?= /* @noEscape */ __('Socials Provider wise Users ') ?></span>
                        </a>
                    </li>
               </ul>
            </div>
            <div class="dashboard-diagram-tab-content mdssp-dashboard-diagram-tab-content">
                <div class="ui-tabs-panel">
                    <div class="dashboard-diagram-switcher-container row">
                        <div class="dashboard-diagram-switcher mdssp-dashboard-diagram-switcher
                        mdssp-dashboard-diagram-switcher-hidden">
                            <label class="label">Report Data</label>
                            <select id="reportType" class="admin__control-select">
                                <option value="providers"><?= /* @noEscape */ __('Providers') ?></option>
                                <option value="socials"><?= /* @noEscape */ __('Social') ?></option>
                            </select>
                        </div>
                        <div class="dashboard-diagram-switcher mdssp-dashboard-diagram-switcher">
                            <label class="label">Chart Type</label>
                            <select id="chartType" class="admin__control-select">
                                <?php foreach ($block->getAvailableCharts() as $chart):
                                    $selected = '';
                                    if ($chart['value'] == $block->getDefaultChart()) {
                                        $selected = 'selected="selected"';
                                    }
                                    ?>
                                    <option value="<?=/* @noEscape */ $chart['value'] ?>" <?=/* @noEscape */$selected?>>
                                        <?= /* @noEscape */ $chart['label'] ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="dashboard-diagram-switcher mdssp-dashboard-diagram-switcher">
                            <div id="reportrange"
                                 style="background: #fff; cursor: pointer; padding: 5px 10px; border: 1px solid #ccc">
                                <i class="fa fa-calendar"></i>
                                <span></span> <i class="fa fa-caret-down"></i>
                            </div>
                        </div>
                        <div class="dashboard-diagram-switcher mdssp-dashboard-diagram-switcher
                        mdssp-dashboard-diagram-switcher-hidden">
                            <label class="label">Group By</label>
                            <select id="reportGroup" class="admin__control-select">
                                <option value="day"><?= /* @noEscape */ __('Day') ?></option>
                                <option value="month"><?= /* @noEscape */ __('Month') ?></option>
                                <option value="year"><?= /* @noEscape */ __('Year') ?></option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-container row">
                        <div id="no_data_available" style="display: none">
                            <div class="no-data-img-box">
                                <div class="no-data-img">
                                    <img src="<?= /* @noEscape */ $block->getViewFileUrl(
                                        'Magedelight_SocialLogin::images/no-data.png'
                                    );?>" />
                                </div>
                                Oops, No Data Available
                            </div>
                        </div>
                        <div id="chart_container">
                            <div id="chart_print_div" class="item-dashboard">
                                <a href="javascript:void(0)"
                                   class="icon-md-print print_chart"
                                   title="<?= /* @noEscape */ __('Print') ?>"></a>
                            </div>
                            <div id="print_div" style="display: none">
                                <div id="chart_image"></div>
                                <div id="chart_table_ui">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>Period</th>
                                                <th>Result</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div id="chart" style="width: 100%; height: 500px"></div>
                        </div>
                    </div>
                    <script type="text/x-magento-init">
                        {
                            "*": {
                                "Magedelight_SocialLogin/js/md_sociallogin_report": {
                                    "url":"<?= /* @noEscape */ $block->getReportUrl() ?>",
                                    "store_id":"<?= /* @noEscape */ $block->getStoreId() ?>",
                                    "defaultPeriod": "<?= /* @noEscape */ $block->getDefaultPeriod() ?>",
                                    "isChartMultiColor": "true",
                                    "defaultChartColor": "<?= /* @noEscape */ $block->getDefaultChartColor() ?>",
                                    "isChartToTable": "<?= /* @noEscape */ $block->isChartToTable() ?>"
                                }
                            }
                        }
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="dashboard-container mdssp-dashboard-container ">
    <div class="dashboard-container row">
        <div class="dashboard-secondary col-m-4">
            <div class="dashboard-item">
                <div class="dashboard-item-title"><?= /* @noEscape */ __('Top Social Providers') ?></div>
                <?= $block->getChildHtml('topSocialProviders') ?>
            </div>
        </div>
    </div>
</div>
