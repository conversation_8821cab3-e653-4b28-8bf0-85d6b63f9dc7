--- a/vendor/mageplaza/module-cron-schedule/Observer/ProcessCronQueueObserver.php	2022-06-22 13:42:05.000000000 +0530
+++ b/vendor/mageplaza/module-cron-schedule/Observer/ProcessCronQueueObserver.php	2025-05-20 16:59:01.908661755 +0530
@@ -44,6 +44,7 @@
 use Magento\Store\Model\ScopeInterface;
 use Mageplaza\CronSchedule\Helper\Data;
 use Psr\Log\LoggerInterface;
+use Laminas\Http\PhpEnvironment\Request as Environment;
 
 /**
  * Class ProcessCronQueueObserver
@@ -91,6 +92,7 @@
         Request $request,
         ShellInterface $shell,
         DateTime $dateTime,
+        Environment $environment,
         PhpExecutableFinderFactory $phpExecutableFinderFactory,
         LoggerInterface $logger,
         State $state,
@@ -119,7 +121,8 @@
             $statFactory,
             $lockManager,
             $eventManager,
-            $retrier
+            $retrier,
+            $environment
         );
     }
 
